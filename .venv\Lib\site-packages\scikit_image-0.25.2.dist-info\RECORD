scikit_image-0.25.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
scikit_image-0.25.2.dist-info/LICENSE.txt,sha256=wK4NZTQWkV93pjZO70iatZ_LMZUUqAZCIoXLQ1llfFs,6587
scikit_image-0.25.2.dist-info/METADATA,sha256=78C5N2fDpfI5xcj4NdqaE63c-rlTOmlxFzo20Gh9uTQ,14355
scikit_image-0.25.2.dist-info/RECORD,,
scikit_image-0.25.2.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scikit_image-0.25.2.dist-info/WHEEL,sha256=1nIT8bOU3dBEtO1OHNUw1PB7s17JH9tAQ93SLqU9JNM,85
skimage/__init__.py,sha256=eqsZ1ul3zQ6Mtv68wiQjHlR---DiVLLi1sQDbAH907I,4255
skimage/__init__.pyi,sha256=79cRJHefG5_lmAzpR1G6ftaz8wppZ__VT0dLvOS0ojc,903
skimage/__pycache__/__init__.cpython-310.pyc,,
skimage/__pycache__/conftest.cpython-310.pyc,,
skimage/_shared/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/_shared/__pycache__/__init__.cpython-310.pyc,,
skimage/_shared/__pycache__/_dependency_checks.cpython-310.pyc,,
skimage/_shared/__pycache__/_geometry.cpython-310.pyc,,
skimage/_shared/__pycache__/_tempfile.cpython-310.pyc,,
skimage/_shared/__pycache__/_warnings.cpython-310.pyc,,
skimage/_shared/__pycache__/compat.cpython-310.pyc,,
skimage/_shared/__pycache__/coord.cpython-310.pyc,,
skimage/_shared/__pycache__/dtype.cpython-310.pyc,,
skimage/_shared/__pycache__/filters.cpython-310.pyc,,
skimage/_shared/__pycache__/tester.cpython-310.pyc,,
skimage/_shared/__pycache__/testing.cpython-310.pyc,,
skimage/_shared/__pycache__/utils.cpython-310.pyc,,
skimage/_shared/__pycache__/version_requirements.cpython-310.pyc,,
skimage/_shared/_dependency_checks.py,sha256=HGRwoD91ZgsnfsvJqJVdDsENXd6UTW4zLjwUc7R3ga0,218
skimage/_shared/_geometry.py,sha256=GQ8JLn9bbSLuomW0YpcBDtIhdsWLfZBy8pY8N5jY-yI,1397
skimage/_shared/_tempfile.py,sha256=AgCezr5TbicsynS-w1q5s1RVT3pI3ukskj29j_CUTeA,791
skimage/_shared/_warnings.py,sha256=Z-LBvoOQlyrqPBlqAvMojuM7ewdjtIi780MbBAL9tnQ,5374
skimage/_shared/compat.py,sha256=El4TahZIVgLuQhdcyD4Ka5iwpa5cScF8VldeEpt_mZU,1006
skimage/_shared/coord.py,sha256=z8EtUPDMMPBglsonAfyJtpWShD8AN6TIGEBY-OpQ6-Y,4453
skimage/_shared/dtype.py,sha256=lbte9u2OGqdycQpn8AkTownFT4r8ALMbA0i348AJkAU,2470
skimage/_shared/fast_exp.cp310-win_amd64.lib,sha256=CyHrDHzfVZRp1V9nmBIlxmJowYO74TAHQ1mN4ZJfFew,1612
skimage/_shared/fast_exp.cp310-win_amd64.pyd,sha256=9G6DQXhmyNcClu6LsJAve4SWulg1a3Ny3PoDSFsjuVM,54272
skimage/_shared/fast_exp.h,sha256=gON6AZ-CaNUs-KHzkelAXxaMQTVQnaz2RaIZo-E6c3Y,1293
skimage/_shared/filters.py,sha256=Dh72CXplsHV1xgiLEriQEeNQn82XEfiQtanYbcSzG1M,5013
skimage/_shared/geometry.cp310-win_amd64.lib,sha256=sfCQpjCuWNKPTXhzxQh21TfK1TJ7hwkaHh7D2uJiu5E,1612
skimage/_shared/geometry.cp310-win_amd64.pyd,sha256=LSfCPajuYlC3a3tXwGsY5iaC-90x_tS8u0BakA5pdeM,117248
skimage/_shared/interpolation.cp310-win_amd64.lib,sha256=iUj6-Qdwk01d9ngbunu8cJ1_b4tXXc8ULUjqYtE8kII,1686
skimage/_shared/interpolation.cp310-win_amd64.pyd,sha256=2R86D6xODIeWUJTPK2XyalMriEdTnlVzbKbeItUsN50,36352
skimage/_shared/tester.py,sha256=1dXlShWNcCX2mt5MCWjpfrx_JmauuOQHtvbxZb59Kk4,3720
skimage/_shared/testing.py,sha256=J7pNw6FNgMg1_4gIw2aE1zcWwt-ziFMLT3dNgIWfgVU,9754
skimage/_shared/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/_shared/tests/__pycache__/__init__.cpython-310.pyc,,
skimage/_shared/tests/__pycache__/test_coord.cpython-310.pyc,,
skimage/_shared/tests/__pycache__/test_dtype.cpython-310.pyc,,
skimage/_shared/tests/__pycache__/test_fast_exp.cpython-310.pyc,,
skimage/_shared/tests/__pycache__/test_geometry.cpython-310.pyc,,
skimage/_shared/tests/__pycache__/test_interpolation.cpython-310.pyc,,
skimage/_shared/tests/__pycache__/test_safe_as_int.cpython-310.pyc,,
skimage/_shared/tests/__pycache__/test_testing.cpython-310.pyc,,
skimage/_shared/tests/__pycache__/test_utils.cpython-310.pyc,,
skimage/_shared/tests/__pycache__/test_version_requirements.cpython-310.pyc,,
skimage/_shared/tests/__pycache__/test_warnings.cpython-310.pyc,,
skimage/_shared/tests/test_coord.py,sha256=ud7i7yzkcWvEx6DD8unZ2jFhDKXLnmUI679b-N8jpD4,3147
skimage/_shared/tests/test_dtype.py,sha256=UKV4BVMdM741geaphIZvhBNrSDbnwIz_z0pwFLnm6o8,383
skimage/_shared/tests/test_fast_exp.py,sha256=o3gw_XENpwKhFRhQXfut0-hfkwFR_7yjws7xVtdzzbw,510
skimage/_shared/tests/test_geometry.py,sha256=RYC4Y5dt_ReGFUH_XMqyzOGkQcvXO2jknsxbgmraZb4,2201
skimage/_shared/tests/test_interpolation.py,sha256=-e7vJHpYHFwOuoE18oOK4IvEKUXc8jbukRICN-ZbrnY,1165
skimage/_shared/tests/test_safe_as_int.py,sha256=UOGEVdBHiCZ8LSGXa8SeJyuhZtQFkGv_R8bjyGmItHc,1465
skimage/_shared/tests/test_testing.py,sha256=2PjLp2hArnfmVGaDaGSipC6Sr7jZ9XGUMxr7K6nRSPQ,4358
skimage/_shared/tests/test_utils.py,sha256=MbkY9pJkJK998Z-3189CKgORpaHFRcNJrPh2cUCqRyo,16132
skimage/_shared/tests/test_version_requirements.py,sha256=e60ymjnASflQcSYuHJ-deGAnRPx9L18UYngUgS1pDBU,1115
skimage/_shared/tests/test_warnings.py,sha256=75A_jYc2HFqcGBu-tQKTwMktthBp_2mL6EpMSapfXnA,1287
skimage/_shared/transform.cp310-win_amd64.lib,sha256=K3Ai7xdMiHMnM8cESTM4-7Lqzet9CZ6VVMeQYhnRdwM,1626
skimage/_shared/transform.cp310-win_amd64.pyd,sha256=99nOvnNdJOk2tii83y6QyqLuka1N5f3tIQKJaP34tjE,114688
skimage/_shared/utils.py,sha256=QQHIRbsUbGNXxzBE6kFFyziv11E7lnhqIGQCH6eFO0w,30546
skimage/_shared/version_requirements.py,sha256=04BfKWjmTgoTdtzEdUZn8Mrqq-c941JffV3olrgLQOU,4485
skimage/_vendored/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/_vendored/__pycache__/__init__.cpython-310.pyc,,
skimage/_vendored/__pycache__/numpy_lookfor.cpython-310.pyc,,
skimage/_vendored/numpy_lookfor.py,sha256=Tq9f9loUwAIf2Gb87hQQ4PT93WLxgyPMQ8ycugiEV90,9964
skimage/color/__init__.py,sha256=Km7t3ppjHW3rzHaLiK0HlDBMfecAUk-UVpYK1huty9Y,135
skimage/color/__init__.pyi,sha256=X7IFHa7W170IKKAe33O1b6-4YATye0Tudf5cWaX0reE,2517
skimage/color/__pycache__/__init__.cpython-310.pyc,,
skimage/color/__pycache__/adapt_rgb.cpython-310.pyc,,
skimage/color/__pycache__/colorconv.cpython-310.pyc,,
skimage/color/__pycache__/colorlabel.cpython-310.pyc,,
skimage/color/__pycache__/delta_e.cpython-310.pyc,,
skimage/color/__pycache__/rgb_colors.cpython-310.pyc,,
skimage/color/adapt_rgb.py,sha256=SKvUBAf2VHrtLtpCGeVacu1sOi4wrOaUJ2MIDHPviTc,2570
skimage/color/colorconv.py,sha256=16X4WXtNPmZfXF_UFd8vKSSng91Qjwy1pGyH_a3tzGE,69819
skimage/color/colorlabel.py,sha256=q73yQTI-IQmhtzpjmJg3-44_DS72Tmb7CP5_MSDm4UY,10805
skimage/color/delta_e.py,sha256=aixaIb_P3RISHbniJyw_kgPzun0EJWrUhx5qoXfJ7wk,13099
skimage/color/rgb_colors.py,sha256=Mb_hq6RzxvPxwDVG2v4-h-1eKfyPZ5lyyseyBNos-eU,4639
skimage/color/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/color/tests/__pycache__/__init__.cpython-310.pyc,,
skimage/color/tests/__pycache__/test_adapt_rgb.cpython-310.pyc,,
skimage/color/tests/__pycache__/test_colorconv.cpython-310.pyc,,
skimage/color/tests/__pycache__/test_colorlabel.cpython-310.pyc,,
skimage/color/tests/__pycache__/test_delta_e.cpython-310.pyc,,
skimage/color/tests/test_adapt_rgb.py,sha256=4nkIMXCajgoWABUmHhSTorq5ZyuR6KJgypZP7bm22eM,2855
skimage/color/tests/test_colorconv.py,sha256=MczXt-Q_l5jx-5NMwvzUTXz5It6qjWeuaSloD_Idu3M,38343
skimage/color/tests/test_colorlabel.py,sha256=SOwgYIa6q8KOPUQt1MLg7ZLV2UWrHsmcHUJcdmqAQjM,11224
skimage/color/tests/test_delta_e.py,sha256=tMRznh12Sc8JZvGkclkL9_CKKOZ0qY_L--if0DlKrPE,8386
skimage/conftest.py,sha256=JkqSj4RNShzvgVztuOcX3xiyfe_qvRsLWOWmwKmctPc,327
skimage/data/README.txt,sha256=OgaFfwPYunbnMTytBBmgub0XaU6Zy_CljrLczYGzxro,289
skimage/data/__init__.py,sha256=wNrAUjN541b8eZHmrjLhBqA_RXox9PVLEumckd3t7fU,401
skimage/data/__init__.pyi,sha256=oCpLHbUhBLnLCdXfpPUjJ6NRB8f06QgUNqj9_00e6Vs,1499
skimage/data/__pycache__/__init__.cpython-310.pyc,,
skimage/data/__pycache__/_binary_blobs.cpython-310.pyc,,
skimage/data/__pycache__/_fetchers.cpython-310.pyc,,
skimage/data/__pycache__/_registry.cpython-310.pyc,,
skimage/data/_binary_blobs.py,sha256=Vx_qPFcmUP6Ceex3h4LHZHZTyVZarpcR0jayA3n8ddU,2254
skimage/data/_fetchers.py,sha256=PSZdnFQ8gVUhDAm14o55zKc8n-Fxx2kYxip3BYtClbo,40055
skimage/data/_registry.py,sha256=pOPvK3Tkz5naS_N_CaIqcdTI8WTsug5TMr9F05IdrUo,16047
skimage/data/astronaut.png,sha256=iEMc2WU8zVOXQbVV-wpGthVYswHUEQQStbwotePqbLU,791555
skimage/data/brick.png,sha256=eWbK8yT2uoQxGNmPegd0bSL2o0NDCt0CM-yl9uqqj88,106634
skimage/data/camera.png,sha256=sHk9Kt2g-mromcA5iUgr_5pC09VpD8fjZI8nldcwwjo,139512
skimage/data/cell.png,sha256=jSOn-4H3zId80J8zA1f8f1lWUTBuhOFyUvbgobP2FRU,74183
skimage/data/chelsea.png,sha256=WWqh58uHXrefQ34xA4HSazOKgcLaI0OXBKc8RlHoxLs,240512
skimage/data/chessboard_GRAY.png,sha256=PlGHB3RRWvTQfYIL2IJzZMcIOb-bVzx0bkhQleiT35A,418
skimage/data/chessboard_RGB.png,sha256=GsAe_y1OUPTtpVot3s3CimV2YjpY16fvhFE8XMGaAzE,1127
skimage/data/clock_motion.png,sha256=8Ckiayi2QugBE9hmIumyFe4Geglm_q9eYGBKHgVzOVU,58784
skimage/data/coffee.png,sha256=zAL4yhiLFnx3WnEBtddn0ecXks92LDPW-hWkWZtajec,466706
skimage/data/coins.png,sha256=-Ndz_Jz6b02OWULcNNCgeI_K7SpP77vtCu9TmNfvTLo,75825
skimage/data/color.png,sha256=fS35k94rT6KnjgTl34BQ9JqcURqnXlmrO9VqycmK734,85584
skimage/data/grass.png,sha256=trYCJCaziTbEOkrAljXNeK8HTpD0L_qCJ6yLdFLTn4k,217893
skimage/data/gravel.png,sha256=xIYVtFG_HmBvvXLAqp-MwPBoq3ER732Tu5sPJYZEDBI,194247
skimage/data/horse.png,sha256=x_tgeJ_jlMSF-EIpHqOyHlDRQPOdbctfuZF8wXgiVFU,16633
skimage/data/hubble_deep_field.jpg,sha256=OhnF3YqSepM0uxIpptY3EbHAx2f7J-IobnyEo-LC9fQ,527940
skimage/data/ihc.png,sha256=-N0ao4fd0fSditE7UJIbI3346bJiYG0lh3Boew75PO8,477916
skimage/data/lbpcascade_frontalface_opencv.xml,sha256=Awl3iaPcuw5A0gue-CU328O2cLan8iaNc1Rw8i4AOpE,51858
skimage/data/lfw_subset.npy,sha256=lWDsL17frAGXP2OoqZ0ABT_s0R4hh34YA4--UA-Ohyw,1000080
skimage/data/logo.png,sha256=8sV_6K8Inwi1ulI9lVc8JuYpBKxZZ_TIhRsn0DNpAWg,179723
skimage/data/microaneurysms.png,sha256=oeG-WapEf4zggvf6gJmXqzaaKxN8tsQgKrxkfHzPZFY,4950
skimage/data/moon.png,sha256=eHOWGdEffrnBZbtdLv1Hcs7lV4EuyEdTLbsdku9x9Xc,50177
skimage/data/motorcycle_disp.npz,sha256=LknIzr_z-iA1mgzGiAyC4cA7uxBtqBoXchgoG8LxE9c,1146173
skimage/data/motorcycle_left.png,sha256=2xjpxBV2F0A8NTemujVd_q_pp-q7a5uUyzP2Ul3UkXk,644701
skimage/data/motorcycle_right.png,sha256=X8kTrocOQqS2YjFLyQTReGvK2OLwubZ9uloilAY1d5c,640373
skimage/data/multipage.tif,sha256=TaCtDT30gHqYRyR9G15WW1DUZIH2Q6-1w3wUgCx4Ew8,940
skimage/data/multipage_rgb.tif,sha256=HSO4RP043ODi0G8wQygXzbheUgcNj1RgorpYrr80oN4,5278
skimage/data/no_time_for_that_tiny.gif,sha256=IKvpS6nkXxjeQWxfvvjR9XpJlgC-QPmiAPriRgEO784,4438
skimage/data/page.png,sha256=NBpvCmFVdmKwJzSptuVuwzqRWyxBiGuXUJ3t8qQ7R6M,47679
skimage/data/phantom.png,sha256=VS_2mBZ6pALM6xeYETBgeiKKCgqnxRkpnqpNXzAbo2w,3386
skimage/data/retina.jpg,sha256=OKB_NvJ_CV6Biup7ltNCAsBRdtMCU8ZnM_LgA3np4OY,269564
skimage/data/rocket.jpg,sha256=wt0N58U4340RHkeWGbEpRk0CadCuX9GMqR0zp_3-qVw,112525
skimage/data/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/data/tests/__pycache__/__init__.cpython-310.pyc,,
skimage/data/tests/__pycache__/test_data.cpython-310.pyc,,
skimage/data/tests/test_data.py,sha256=bDoWCCW1HXjETJhXLCoTOkx6tALWk8JNArG_m4ZdjuU,5549
skimage/data/text.png,sha256=vYSqOm48mIeFDUXWBslrLllDP771AzhXC2PDGeZo5tE,42704
skimage/draw/__init__.py,sha256=ttVk7k4-lp6VHiQkryFHRQMO3ZGFz79rEApTMuhPZIY,166
skimage/draw/__init__.pyi,sha256=RSelsMrtOSR7OoidfLQOLksvC0X8_Fku0xG7rmVeiJg,1008
skimage/draw/__pycache__/__init__.cpython-310.pyc,,
skimage/draw/__pycache__/_polygon2mask.cpython-310.pyc,,
skimage/draw/__pycache__/_random_shapes.cpython-310.pyc,,
skimage/draw/__pycache__/draw.cpython-310.pyc,,
skimage/draw/__pycache__/draw3d.cpython-310.pyc,,
skimage/draw/__pycache__/draw_nd.cpython-310.pyc,,
skimage/draw/_draw.cp310-win_amd64.lib,sha256=xDTbzdJzgUHrS4hHPRgux-6Vv__kk6VybGXYFny4RHY,1566
skimage/draw/_draw.cp310-win_amd64.pyd,sha256=ERSTuVjRJeQA18QySfZDpS2RZUnwfQi9FnDAQdmI5ok,252928
skimage/draw/_polygon2mask.py,sha256=grr8AVrBKfmOagZ4VWv6e9WfHjY2Cwjv7MSinQyKLrg,2546
skimage/draw/_random_shapes.py,sha256=Fiz6g857SN4ug2ds-ZTLMnN0PY9qXV2vO4viwUMAGhE,16469
skimage/draw/draw.py,sha256=OIzokC5gxu6UEjyMQHUvSAXP61MHu5Xvd2LaUC5eYkk,34645
skimage/draw/draw3d.py,sha256=lRv6piXW_KQ4R5UtunZGu3TgMmxlJjyx0UY_fXCUGNs,3394
skimage/draw/draw_nd.py,sha256=ZdxCHm0XBFKHZ7FtGnIuSZZPTYY2-Gvuj0iaIBBi2VA,3800
skimage/draw/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/draw/tests/__pycache__/__init__.cpython-310.pyc,,
skimage/draw/tests/__pycache__/test_draw.cpython-310.pyc,,
skimage/draw/tests/__pycache__/test_draw3d.cpython-310.pyc,,
skimage/draw/tests/__pycache__/test_draw_nd.cpython-310.pyc,,
skimage/draw/tests/__pycache__/test_polygon2mask.cpython-310.pyc,,
skimage/draw/tests/__pycache__/test_random_shapes.cpython-310.pyc,,
skimage/draw/tests/test_draw.py,sha256=vVjKDkij1FHcHrpBE7Xyk-pp0zeDy7tx_-tTEydFtfo,42397
skimage/draw/tests/test_draw3d.py,sha256=i4buoNxZp0BgaGUuVRnNsVqWSkjQPPZSGTJ7eHCg0HI,6783
skimage/draw/tests/test_draw_nd.py,sha256=TYTNh-V5sxiL_qbCxLA7A9f2faUZkplEcyPHyT2Pjcc,503
skimage/draw/tests/test_polygon2mask.py,sha256=9WIoDsMI2K3qqGNyRCJWrvqr2zrqcgrVen4kLz8NHK4,344
skimage/draw/tests/test_random_shapes.py,sha256=dPmTB9tWXkittyFc7h7-_Pohc2dlLdxt2TXMh3XByxw,5659
skimage/exposure/__init__.py,sha256=08xHtjudhzg2LlhaxcEE-pisCuC7Zt5HzDKRyWhN9jE,174
skimage/exposure/__init__.pyi,sha256=RB3Zc45VxrLGRCneaIU7pDApGFZhhz6aT_Z94q43a8Y,711
skimage/exposure/__pycache__/__init__.cpython-310.pyc,,
skimage/exposure/__pycache__/_adapthist.cpython-310.pyc,,
skimage/exposure/__pycache__/exposure.cpython-310.pyc,,
skimage/exposure/__pycache__/histogram_matching.cpython-310.pyc,,
skimage/exposure/_adapthist.py,sha256=ZrlKJ6gNNEmdzectGaP72Y-EylvAVoimGa0NPn1TeXg,11288
skimage/exposure/exposure.py,sha256=GtFOJkfvv4QY2eEd0-2KNy2j5E_UEbnXI7iOGwJQU4g,28634
skimage/exposure/histogram_matching.py,sha256=idqamDruw9JwllVh5h_zaU3Lf4_IdFOeb98aumsA33M,3287
skimage/exposure/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/exposure/tests/__pycache__/__init__.cpython-310.pyc,,
skimage/exposure/tests/__pycache__/test_exposure.cpython-310.pyc,,
skimage/exposure/tests/__pycache__/test_histogram_matching.cpython-310.pyc,,
skimage/exposure/tests/test_exposure.py,sha256=TTRDOj4glJZgvJ8nM3Y-YfoyO2K0s9eR9dT_prUpQEs,38480
skimage/exposure/tests/test_histogram_matching.py,sha256=NJTA1HNxCYox8xJjeHwaGOgia3sX3saYvcLyGVHXp-4,5280
skimage/feature/__init__.py,sha256=596PodpSpSGiCsev88K7rul4NHIepy6msqiwrn5iZPA,183
skimage/feature/__init__.pyi,sha256=6FS_7Z1e90Ofey314sZtjgBzXPaKSfDEK4doK94heaM,2236
skimage/feature/__pycache__/__init__.cpython-310.pyc,,
skimage/feature/__pycache__/_basic_features.cpython-310.pyc,,
skimage/feature/__pycache__/_canny.cpython-310.pyc,,
skimage/feature/__pycache__/_daisy.cpython-310.pyc,,
skimage/feature/__pycache__/_fisher_vector.cpython-310.pyc,,
skimage/feature/__pycache__/_hog.cpython-310.pyc,,
skimage/feature/__pycache__/_orb_descriptor_positions.cpython-310.pyc,,
skimage/feature/__pycache__/blob.cpython-310.pyc,,
skimage/feature/__pycache__/brief.cpython-310.pyc,,
skimage/feature/__pycache__/censure.cpython-310.pyc,,
skimage/feature/__pycache__/corner.cpython-310.pyc,,
skimage/feature/__pycache__/haar.cpython-310.pyc,,
skimage/feature/__pycache__/match.cpython-310.pyc,,
skimage/feature/__pycache__/orb.cpython-310.pyc,,
skimage/feature/__pycache__/peak.cpython-310.pyc,,
skimage/feature/__pycache__/sift.cpython-310.pyc,,
skimage/feature/__pycache__/template.cpython-310.pyc,,
skimage/feature/__pycache__/texture.cpython-310.pyc,,
skimage/feature/__pycache__/util.cpython-310.pyc,,
skimage/feature/_basic_features.py,sha256=5Caems-vn3Za4BRrUP-3KN7ymJSkaQF-88ll_Y5ZwdA,6976
skimage/feature/_canny.py,sha256=3P9aAOIr5sRLWoBy9mYNE-Rrq0lIoJER6KWisBBVQOg,9728
skimage/feature/_canny_cy.cp310-win_amd64.lib,sha256=xVK8sOoPObgdWEwPv1a-2zmDRJFb9k99hYxqJ0hvghE,1626
skimage/feature/_canny_cy.cp310-win_amd64.pyd,sha256=JPbfsm9QhRLnMfFkFlOMMP0QeNPtQEb1FNW98TSHNQk,169472
skimage/feature/_cascade.cp310-win_amd64.lib,sha256=TuImVhAkuSeceuUIbjnP9vdd1m95WF3uDkB9Q0_lUD4,1612
skimage/feature/_cascade.cp310-win_amd64.pyd,sha256=4PuBj7CNkyG5XBgyECo8KfuQ8HMvKFI8Flcem_qiIhg,219136
skimage/feature/_daisy.py,sha256=M6ElZBj6-EbhPeb8frO_8CNX4Zv0AbthMEyWGD5vZ4I,10313
skimage/feature/_fisher_vector.py,sha256=2Strunq8us8ThMJOQ0IssPqo5WQDEB3ybSoZPM2db5k,10776
skimage/feature/_haar.cp310-win_amd64.lib,sha256=z9Y5AvY01nMm82vHRE7DpVkJ9orjtxPABApqNsYN6jQ,1566
skimage/feature/_haar.cp310-win_amd64.pyd,sha256=2vvMUPifEYi38SES9M934Gb2tevf5jlbbv1I5sww9WU,339456
skimage/feature/_hessian_det_appx.cp310-win_amd64.lib,sha256=eYNER2NTC8h5LaqSp-XrDMKy26S9LqWaJ2HucPcUIjI,1746
skimage/feature/_hessian_det_appx.cp310-win_amd64.pyd,sha256=vge1e_3yYIXdUcgOaiGEhYdinjSRDM5XoqHoTbWZz94,53248
skimage/feature/_hog.py,sha256=W4qp4mV-72Bd7nlUtJecA7EJhBDF6A_aQjtCp3sbAqY,13549
skimage/feature/_hoghistogram.cp310-win_amd64.lib,sha256=7tSO5J6Ap6lpQhILnEdHoJ5vuepK8GywsPdEsBWgQfE,1686
skimage/feature/_hoghistogram.cp310-win_amd64.pyd,sha256=-6eFhQFW_ZQGx42usnLlVe91wRoVb12ArUFs5gV37kw,173056
skimage/feature/_orb_descriptor_positions.py,sha256=oQuEejpASkBwZ5lh--aSA32hRP7GECpPcDsXB_WBb4s,460
skimage/feature/_sift.cp310-win_amd64.lib,sha256=OFnhCZukwAVSNi2bai5n6h6x9VCjMbBLt9yvoryqQh0,1566
skimage/feature/_sift.cp310-win_amd64.pyd,sha256=q28kuKu9tfc3jzJZ0JDtmxGObfUUB1P6lrHoyi6_m1g,217600
skimage/feature/_texture.cp310-win_amd64.lib,sha256=VgH2eTCKXdapY8t7hPi5B5gI5O1zOID0dAmCnSN1kDI,1612
skimage/feature/_texture.cp310-win_amd64.pyd,sha256=DDpurGhuLoucn1w0ggEhkBipIy4vwOBzV_gWbuWXFjk,250368
skimage/feature/blob.py,sha256=d0qi9mJNASLnVr308O8deyqQNdYEweZ-y4V05e4Lxsw,28600
skimage/feature/brief.py,sha256=jxytKb-rRWX7Kd5QlPlBLPON1BHuzghPGpsw7_eai5M,8331
skimage/feature/brief_cy.cp310-win_amd64.lib,sha256=NpeuR_m7OpVct_wSTJwB2SHIzbfmbxW4Ofu4tPNekRI,1612
skimage/feature/brief_cy.cp310-win_amd64.pyd,sha256=NofzGGteBGBjbmsPCtDXsi_e4eLI6cMQny8xw0uzXzc,160256
skimage/feature/censure.py,sha256=SFZ7Lyd5D0wUzadsVSxDrVkna_i8fiNzw9oh8hgwuS4,12346
skimage/feature/censure_cy.cp310-win_amd64.lib,sha256=W9plcaAA4q4ffeKlV5d1E3YRgcrLZD125y-I8HewO-o,1642
skimage/feature/censure_cy.cp310-win_amd64.pyd,sha256=n2cbqS_hiOSqVWFWQ_HFdRjsJI9efJQSxAMn6a8K7gM,139264
skimage/feature/corner.py,sha256=B4fPRS5a78QML9L66k-mQane5btmlnKP55evCNzZnGI,46924
skimage/feature/corner_cy.cp310-win_amd64.lib,sha256=sCk3JErOwZpYEMvm_G0P8sHDh-spC2gwmnoetgwOHOE,1626
skimage/feature/corner_cy.cp310-win_amd64.pyd,sha256=r_4JqaS_tSLNgUOVIm8sKOxQzYHAnpcFmlN41e0NqbQ,231936
skimage/feature/haar.py,sha256=K66YuzcaxHHfw5FXnEs4ZiEX7JYBh38wMhWpLaMj8zU,13260
skimage/feature/match.py,sha256=BUlHCbwPlI74MEM4sBICq9N5bgS8HBkwUj_8wLlYcyU,4126
skimage/feature/orb.py,sha256=K34X0JMBT60ObhqVwydQjiCy9aAajpxvmNO13wVpKvw,13514
skimage/feature/orb_cy.cp310-win_amd64.lib,sha256=BNKvd4uoQyyP6qRALCg2eDGPBpO8uZGiDFjYPX-ZeQA,1582
skimage/feature/orb_cy.cp310-win_amd64.pyd,sha256=NKdHLHe4Gv9wwOt_1VoFvEqlEl9WqK78wkutYW0kVnw,168448
skimage/feature/orb_descriptor_positions.txt,sha256=S_LZW-3MmEpxczRlk40BD0UHCftjCyJ3u8rRmBbsYCQ,3096
skimage/feature/peak.py,sha256=GTss8tMIBcsSUMBCfkpDsqIhzX77S6foJ-hcw-u3n80,14898
skimage/feature/sift.py,sha256=b6X1_g5Go-f-2jX5kx1D6Gc9VGzIQqXTjIT_DZKx3U4,30178
skimage/feature/template.py,sha256=Dl_LCuK4gdWdd4RUSQD1NkgEsmWmR9YCCr8LvFoJdGA,6757
skimage/feature/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/feature/tests/__pycache__/__init__.cpython-310.pyc,,
skimage/feature/tests/__pycache__/test_basic_features.cpython-310.pyc,,
skimage/feature/tests/__pycache__/test_blob.cpython-310.pyc,,
skimage/feature/tests/__pycache__/test_brief.cpython-310.pyc,,
skimage/feature/tests/__pycache__/test_canny.cpython-310.pyc,,
skimage/feature/tests/__pycache__/test_cascade.cpython-310.pyc,,
skimage/feature/tests/__pycache__/test_censure.cpython-310.pyc,,
skimage/feature/tests/__pycache__/test_corner.cpython-310.pyc,,
skimage/feature/tests/__pycache__/test_daisy.cpython-310.pyc,,
skimage/feature/tests/__pycache__/test_fisher_vector.cpython-310.pyc,,
skimage/feature/tests/__pycache__/test_haar.cpython-310.pyc,,
skimage/feature/tests/__pycache__/test_hog.cpython-310.pyc,,
skimage/feature/tests/__pycache__/test_match.cpython-310.pyc,,
skimage/feature/tests/__pycache__/test_orb.cpython-310.pyc,,
skimage/feature/tests/__pycache__/test_peak.cpython-310.pyc,,
skimage/feature/tests/__pycache__/test_sift.cpython-310.pyc,,
skimage/feature/tests/__pycache__/test_template.cpython-310.pyc,,
skimage/feature/tests/__pycache__/test_texture.cpython-310.pyc,,
skimage/feature/tests/__pycache__/test_util.cpython-310.pyc,,
skimage/feature/tests/test_basic_features.py,sha256=Kx30TFoN6hCCU_pRMTFJ-e9j-LlUcBlBYgkoml3Qc20,2154
skimage/feature/tests/test_blob.py,sha256=0OVg7srxZmX8FOb9iJhYm4q_wPuslz2wBbj1UmnXQjQ,16787
skimage/feature/tests/test_brief.py,sha256=--Dgom4iPXnsYhP5EFLtS8x5KUKr0xp4tAMbjskI5SU,3312
skimage/feature/tests/test_canny.py,sha256=Tx-IqKUDZW67VBHRmcIeimfTdu6vtrmH8vXklWMqkQo,6573
skimage/feature/tests/test_cascade.py,sha256=vEZX2vc7l35VGvqrJeOTv4B_n15p8R_W1QN1dvwxYoo,532
skimage/feature/tests/test_censure.py,sha256=c9UTOgDoja6ej8b1p93gTgBfoOdZrR_WBoMGBRVH2RI,3256
skimage/feature/tests/test_corner.py,sha256=g5NTDP2UrwkFCOOps5Qkv1Cihzd44NQ20eHIDmnt2nY,25007
skimage/feature/tests/test_daisy.py,sha256=vGdiwjsgKkCYVmnwjMmP9Ropf9jsTGkA4OnxdTn4DfQ,3496
skimage/feature/tests/test_fisher_vector.py,sha256=tDZd8NLNvZyjhJ7LeE_eyspzabMFbVZ6ssQzmtkqX3g,5921
skimage/feature/tests/test_haar.py,sha256=1gI9eOxApJLLrviXueCa6KqQlT0gFCyaq6YideKzAvM,6841
skimage/feature/tests/test_hog.py,sha256=OqeSdBQ-Xe-MFMg3sHXQi9kLejCfcG2Q6lMYUyfeI9g,12080
skimage/feature/tests/test_match.py,sha256=G1dMLSipwswQH6zeqwrni_dZ0yuYc969mdeRv86dhCI,8386
skimage/feature/tests/test_orb.py,sha256=M3go_XJwWNupGxdhoxIpy_j41VkuZbXnv3W5hBLqfDA,6448
skimage/feature/tests/test_peak.py,sha256=z60deYJeOvxJ1kLGk5M84cPaAruH926S8wIuX_mfRRM,22634
skimage/feature/tests/test_sift.py,sha256=3VfE57SoT5DH98a40qNuTr5sp67_PpttdQ-u3f9A4lM,5932
skimage/feature/tests/test_template.py,sha256=dqI3w-6Fz7XUeXRXFsn10_sZ1i_91LdWVDpKu9qMcIM,6323
skimage/feature/tests/test_texture.py,sha256=T5P-WHylmFQPtMB-0629V-2GwLMQBtEosZPVRqQ8VGM,13964
skimage/feature/tests/test_util.py,sha256=BiDymskMSbkyvZ43bhIE8qtGzfOvStqWfg1nGPisveA,6387
skimage/feature/texture.py,sha256=C8fG6gJfp9ac0jxcZuNCUusO2o7FAxF0BtVCpMk-UNc,21106
skimage/feature/util.py,sha256=GMcRzxy9kZaj6yCJSpqsp1frMzI8QL-9BRvHYvXeAhM,7281
skimage/filters/__init__.py,sha256=D-a69O0p908RtvU6wH_up9Uq9K_Vj4Vm7OmrErFiU-Y,170
skimage/filters/__init__.pyi,sha256=K6q1d5smvqtkWBWANE07XBycOb-nuv-PC05tAkrFS90,2267
skimage/filters/__pycache__/__init__.cpython-310.pyc,,
skimage/filters/__pycache__/_fft_based.cpython-310.pyc,,
skimage/filters/__pycache__/_gabor.cpython-310.pyc,,
skimage/filters/__pycache__/_gaussian.cpython-310.pyc,,
skimage/filters/__pycache__/_median.cpython-310.pyc,,
skimage/filters/__pycache__/_rank_order.cpython-310.pyc,,
skimage/filters/__pycache__/_sparse.cpython-310.pyc,,
skimage/filters/__pycache__/_unsharp_mask.cpython-310.pyc,,
skimage/filters/__pycache__/_window.cpython-310.pyc,,
skimage/filters/__pycache__/edges.cpython-310.pyc,,
skimage/filters/__pycache__/lpi_filter.cpython-310.pyc,,
skimage/filters/__pycache__/ridges.cpython-310.pyc,,
skimage/filters/__pycache__/thresholding.cpython-310.pyc,,
skimage/filters/_fft_based.py,sha256=7EEt_tiGnwXaH1YVlp-8KofGPHDXnm3D_ktlyB4DLPo,6898
skimage/filters/_gabor.py,sha256=UQJCIvHKs-52fb0e9hVcmZGkflOKBgq7mCF_1_BWr8A,7940
skimage/filters/_gaussian.py,sha256=-WxlahxCPg-hzB63SN3XjVf8cjnp2o7bMDVYSNUPfic,6205
skimage/filters/_median.py,sha256=jjEMVLOk7hIB3kKdREOfy80HIbQLETTIDynt1_mnT6w,3046
skimage/filters/_multiotsu.cp310-win_amd64.lib,sha256=0cIRT7Ke6HtbaptR-8ZTXRk8gAySuPk1jc6YXympdpE,1642
skimage/filters/_multiotsu.cp310-win_amd64.pyd,sha256=XHerChhbc1uanq1FnB5C9QOPXvohpQz1LFqmcRT_lJE,154624
skimage/filters/_rank_order.py,sha256=8rY2JdfKfJDySjsCX1yS8EvgkN5ZubIUGe-bMkiDKzE,2114
skimage/filters/_sparse.py,sha256=o6poPSNIF6zxPy5Du_vHvqP8-kSiC_xFGTZ0hYA6XPQ,4793
skimage/filters/_unsharp_mask.py,sha256=AX2S4tCsyPsVzB6vYXCsEU8dYFAGuuiYsq54udB7LVE,5652
skimage/filters/_window.py,sha256=O6Q9p4sA3jWTiMSzAj-VNFq8Jr_biGBlK4HC6uOuYQY,4482
skimage/filters/edges.py,sha256=8LC8A9QZLqNiAhwyrq1O06VO8Ud0yOuVu8wXnVFmDV8,26502
skimage/filters/lpi_filter.py,sha256=O_x1zPkwsnlJVx7tv2pHEwvfGujiJJC4ZaRIZaobe8g,8198
skimage/filters/rank/__init__.py,sha256=YT6hUNpQc9nB5Fo0_L6HD99pJ6YxFSra4OH63Z0Plt4,1637
skimage/filters/rank/__pycache__/__init__.cpython-310.pyc,,
skimage/filters/rank/__pycache__/_percentile.cpython-310.pyc,,
skimage/filters/rank/__pycache__/bilateral.cpython-310.pyc,,
skimage/filters/rank/__pycache__/generic.cpython-310.pyc,,
skimage/filters/rank/_percentile.py,sha256=uuD1QvtEzVB8M9CrRfdOBs-aF14aX8c4J_bKepF-IuM,14380
skimage/filters/rank/bilateral.py,sha256=7FKlzDv3DId3Shc3cBNBb1zW4wglqUwaqBSEXjB0A9k,8084
skimage/filters/rank/bilateral_cy.cp310-win_amd64.lib,sha256=YYuBxfXDSiL2bqHSw66MW_ppWcQUlaTSfbuddg6DeVo,1672
skimage/filters/rank/bilateral_cy.cp310-win_amd64.pyd,sha256=IxCPgezVn-lGtlfUEv6u6XDqSrafb64yVnG1zK5x1UU,331776
skimage/filters/rank/core_cy.cp310-win_amd64.lib,sha256=s69MCMHfusSDFW8rgCoHtOwHFWPo1ZYIANeSCrdoXD4,1596
skimage/filters/rank/core_cy.cp310-win_amd64.pyd,sha256=H1UWfp3IgDinXdOTAqjHMN2Xy_EVdfY8ifvHQR0ah84,429056
skimage/filters/rank/core_cy_3d.cp310-win_amd64.lib,sha256=_DQUQ0jZE3Dn6W68WgdB5O-JYMgdkmzkunLPjpbIkQQ,1642
skimage/filters/rank/core_cy_3d.cp310-win_amd64.pyd,sha256=obih3ZYQIWmA0f41ptZyBX3eMpWfbyV-W9vc_y_fndY,214528
skimage/filters/rank/generic.py,sha256=YPQY8YwG4SUgf9ATDUESRJ-VFJvzSLPnhz5pPrFaVi4,57736
skimage/filters/rank/generic_cy.cp310-win_amd64.lib,sha256=kGZgtonHtwizqKcRv9_ArIcZ40KlxIU8b4YV7UkvdsQ,1642
skimage/filters/rank/generic_cy.cp310-win_amd64.pyd,sha256=BGpzT9jfU8LeBtBJ1M57aCbuxwntx5gXjzVIeeaTImM,2154496
skimage/filters/rank/percentile_cy.cp310-win_amd64.lib,sha256=Po8aTD_UbT-RT3mBNein15yixv0D0zAnJmN6zNBgKjQ,1686
skimage/filters/rank/percentile_cy.cp310-win_amd64.pyd,sha256=9zlz-gySisx0ErIhwTtbF6oYenSlL8gzqXxliHpofxA,665600
skimage/filters/rank/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/filters/rank/tests/__pycache__/__init__.cpython-310.pyc,,
skimage/filters/rank/tests/__pycache__/test_rank.cpython-310.pyc,,
skimage/filters/rank/tests/test_rank.py,sha256=KkbPx32HKIHhh5Zm0jEUsaRmrl2DSiM2ilK1WNeMR2k,40125
skimage/filters/ridges.py,sha256=m2UJ_zdsbulH8RgpKSDgPzUKZ_-lBHVlZbeUcXeLsnE,14640
skimage/filters/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/filters/tests/__pycache__/__init__.cpython-310.pyc,,
skimage/filters/tests/__pycache__/test_correlate.cpython-310.pyc,,
skimage/filters/tests/__pycache__/test_edges.cpython-310.pyc,,
skimage/filters/tests/__pycache__/test_fft_based.cpython-310.pyc,,
skimage/filters/tests/__pycache__/test_gabor.cpython-310.pyc,,
skimage/filters/tests/__pycache__/test_gaussian.cpython-310.pyc,,
skimage/filters/tests/__pycache__/test_lpi_filter.cpython-310.pyc,,
skimage/filters/tests/__pycache__/test_median.cpython-310.pyc,,
skimage/filters/tests/__pycache__/test_ridges.cpython-310.pyc,,
skimage/filters/tests/__pycache__/test_thresholding.cpython-310.pyc,,
skimage/filters/tests/__pycache__/test_unsharp_mask.cpython-310.pyc,,
skimage/filters/tests/__pycache__/test_window.cpython-310.pyc,,
skimage/filters/tests/test_correlate.py,sha256=IFlaJMWLHyy0lQSfKUEe0iiyzNPJxrSJxbnqCqfJoNw,2065
skimage/filters/tests/test_edges.py,sha256=Q3_hpGF1JWuAChoCP7K3y00iB51jR_Se_-SkMG75Ics,22127
skimage/filters/tests/test_fft_based.py,sha256=ObCU57leDNlp1kjUFJh6qDnho1HGXn-zglU0xcoNBF8,14874
skimage/filters/tests/test_gabor.py,sha256=gJn-aK-PRRpdO_CKRSGFSvV6WCGE6XM5anrjuhvg9SY,3797
skimage/filters/tests/test_gaussian.py,sha256=UCK9GflvzCareZaS6kQV-2qAciI2RPnbDEQtBg8lqPQ,5620
skimage/filters/tests/test_lpi_filter.py,sha256=4bJ-oeALsSJZku_MqRokpoSZ5-2P2WLJtibrQzqOYRQ,3194
skimage/filters/tests/test_median.py,sha256=sjKjsq_IQfuoZMGV9zBaxZ0jAyufR6Eqvok1cXy3Zfo,2187
skimage/filters/tests/test_ridges.py,sha256=LrumY76DIXl_C-hGhcCY1isznOX0CjfBCBUAM7tDEUI,9830
skimage/filters/tests/test_thresholding.py,sha256=9fhSWp1X6t_gyNd2ReHO4hZMYuWZpENXbA16rCROBJg,27230
skimage/filters/tests/test_unsharp_mask.py,sha256=S5lb7xP3JaUv04Qj7JKuVPv8K4nuvdey3-HW8csnksc,5213
skimage/filters/tests/test_window.py,sha256=CzAP21ae28D5jt_XHRRBXWPO1EPsfzxkXUwPYJX9TNQ,1678
skimage/filters/thresholding.py,sha256=xM4ObHFS9L4LkAnWzBimSGcLVSo30N1t-HYElXXbGUY,49244
skimage/future/__init__.py,sha256=vh6tzyhpNng6iRKeCdAUoN_w4kkDR_ty1-oA_FfPbYs,519
skimage/future/__init__.pyi,sha256=Gf7-bzUT6bHeIqdDzSqE8Dq0gqRfKJjA9G8UNdiDQ4Q,507
skimage/future/__pycache__/__init__.cpython-310.pyc,,
skimage/future/__pycache__/manual_segmentation.cpython-310.pyc,,
skimage/future/__pycache__/trainable_segmentation.cpython-310.pyc,,
skimage/future/manual_segmentation.py,sha256=rqsHz5DT0lNHDyeGIfcCXcDrZUmI3Lc5KVoxnSF5fe0,7845
skimage/future/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/future/tests/__pycache__/__init__.cpython-310.pyc,,
skimage/future/tests/__pycache__/test_trainable_segmentation.cpython-310.pyc,,
skimage/future/tests/test_trainable_segmentation.py,sha256=e2NXxSe_AKyC4g1wER-sIqrmvfOOhP8adLjQWv8NyNQ,4367
skimage/future/trainable_segmentation.py,sha256=ALGJFhCGWnOMSChKGtwS7jGSUnhCqyLxiKelEVOmeU8,5768
skimage/graph/__init__.py,sha256=wvwxd_yPsvKuGPI7QonIwnarOZla3M35Zuh1CPXoEcs,350
skimage/graph/__init__.pyi,sha256=MsaMsDSGLm3wIw4rJCQP_4E_X71sveSSwo85ABRnzRI,809
skimage/graph/__pycache__/__init__.cpython-310.pyc,,
skimage/graph/__pycache__/_graph.cpython-310.pyc,,
skimage/graph/__pycache__/_graph_cut.cpython-310.pyc,,
skimage/graph/__pycache__/_graph_merge.cpython-310.pyc,,
skimage/graph/__pycache__/_ncut.cpython-310.pyc,,
skimage/graph/__pycache__/_rag.cpython-310.pyc,,
skimage/graph/__pycache__/mcp.cpython-310.pyc,,
skimage/graph/__pycache__/spath.cpython-310.pyc,,
skimage/graph/_graph.py,sha256=gEKqlhKuZtakZzQQHiQMAAA-qMLj6M4d_uG6FNNqe84,9443
skimage/graph/_graph_cut.py,sha256=E57UOOLHWdNchF-JnY2MiKsLn1QVSIp4mSkhhTl34yo,10462
skimage/graph/_graph_merge.py,sha256=P_LsAbbpCOgHHahyYpQM-5YEn670pJJPoTdoONk0_ek,4442
skimage/graph/_mcp.cp310-win_amd64.lib,sha256=EcmfPDM-0iOV_PgmcbUtBYI-mvQuvszKw0LH2ZvMNEI,1552
skimage/graph/_mcp.cp310-win_amd64.pyd,sha256=qCf4mee6y0o13zlG0MPX5bIXp5R5f1uIfmp9o5aGvDw,318976
skimage/graph/_ncut.py,sha256=3YRc5B6Y0dOoKBBUuK66yRIm8_04puL2MgMAz5SK4m8,1889
skimage/graph/_ncut_cy.cp310-win_amd64.lib,sha256=NCILT7rTIXU7PoeeYFBHSIdjSapCmp4kBx4fihmmn4w,1612
skimage/graph/_ncut_cy.cp310-win_amd64.pyd,sha256=UeYG-LFMLgxKMnIseANYBLS2UV97bJhpS2ehDnNdJ94,182784
skimage/graph/_rag.py,sha256=xcVvjEINk2VQfIducEAzbhlOXmlUzKixr1w78j8y70Y,21207
skimage/graph/_spath.cp310-win_amd64.lib,sha256=MRnWdu1kw7b-ZnVbpgHDmAL0lLV05khBMHUISH4a-AE,1582
skimage/graph/_spath.cp310-win_amd64.pyd,sha256=hT5EbY-hVdIzGj7fY86K9F_VKVWvhg-cy1YE66aye4E,148480
skimage/graph/heap.cp310-win_amd64.lib,sha256=kfH6mXP08r7xZMkqPFWx96ftMGyRHhWDX9TE_kTKdpE,1552
skimage/graph/heap.cp310-win_amd64.pyd,sha256=vB2AbVlwKupvd9mjvawCXNEEvdhnv6DRi5VMp1S-tN8,82432
skimage/graph/mcp.py,sha256=Tdc6rXFY6EZLF4aO-ZgrKu_g0SrcCOv4qTH2hTHsKlk,3270
skimage/graph/spath.py,sha256=7RrpIsC4vEnOcx-Bt0DvePULTDTwYrrmVOJnBkF-RNY,3482
skimage/graph/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/graph/tests/__pycache__/__init__.cpython-310.pyc,,
skimage/graph/tests/__pycache__/test_anisotropy.cpython-310.pyc,,
skimage/graph/tests/__pycache__/test_connect.cpython-310.pyc,,
skimage/graph/tests/__pycache__/test_flexible.cpython-310.pyc,,
skimage/graph/tests/__pycache__/test_heap.cpython-310.pyc,,
skimage/graph/tests/__pycache__/test_mcp.cpython-310.pyc,,
skimage/graph/tests/__pycache__/test_pixel_graph.cpython-310.pyc,,
skimage/graph/tests/__pycache__/test_rag.cpython-310.pyc,,
skimage/graph/tests/__pycache__/test_spath.cpython-310.pyc,,
skimage/graph/tests/test_anisotropy.py,sha256=7UTP8v-KI0pLv4ZKM6UopNuZPqzhcKsB1oGgzocqyTM,3715
skimage/graph/tests/test_connect.py,sha256=dUpgL6_laaN2LSzXkdtIL0KV2p5ArR61dzV5RowHhHU,2439
skimage/graph/tests/test_flexible.py,sha256=ycEdLv9-1xfudb6fDZI4dgQemdKr5HBQ1iOGp8Sj9VQ,1612
skimage/graph/tests/test_heap.py,sha256=R34Sozy8TttXn5DCblHVCPK5nz75MrzuSVhplliP4-8,1155
skimage/graph/tests/test_mcp.py,sha256=9aUqm_mSN5IUAz-CdR0JjfyKCkP2GosCfUBnIjAaEPw,5590
skimage/graph/tests/test_pixel_graph.py,sha256=rrhWGaQY9OPyfZ4L1ol1u-_czPlSB5uF3SgoPp_0ij0,3849
skimage/graph/tests/test_rag.py,sha256=R1wN7Y5LlDbAqU55C-d2ZGxtYdAClPoCGhjJcsE31uA,8119
skimage/graph/tests/test_spath.py,sha256=AeUrsbYMvoBfWRcx1-Gcate1MNOTeNqD94H0nfoEokE,858
skimage/io/__init__.py,sha256=c0-l8ZiPVdTuGPvMjd9vKZ80LGe65fjbA9qGlPtUka4,1052
skimage/io/__pycache__/__init__.cpython-310.pyc,,
skimage/io/__pycache__/_image_stack.cpython-310.pyc,,
skimage/io/__pycache__/_io.cpython-310.pyc,,
skimage/io/__pycache__/collection.cpython-310.pyc,,
skimage/io/__pycache__/manage_plugins.cpython-310.pyc,,
skimage/io/__pycache__/sift.cpython-310.pyc,,
skimage/io/__pycache__/util.cpython-310.pyc,,
skimage/io/_image_stack.py,sha256=FibEhJ_OW8gvyco8XAC2bq0gBn6Z7x0ZM4I4p9Q4ExA,605
skimage/io/_io.py,sha256=vLAwrLMAfuDye1NRzUbQe3TbyTxwvfWJxRjPSx9yeTc,9275
skimage/io/_plugins/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/io/_plugins/__pycache__/__init__.cpython-310.pyc,,
skimage/io/_plugins/__pycache__/fits_plugin.cpython-310.pyc,,
skimage/io/_plugins/__pycache__/gdal_plugin.cpython-310.pyc,,
skimage/io/_plugins/__pycache__/imageio_plugin.cpython-310.pyc,,
skimage/io/_plugins/__pycache__/imread_plugin.cpython-310.pyc,,
skimage/io/_plugins/__pycache__/matplotlib_plugin.cpython-310.pyc,,
skimage/io/_plugins/__pycache__/pil_plugin.cpython-310.pyc,,
skimage/io/_plugins/__pycache__/simpleitk_plugin.cpython-310.pyc,,
skimage/io/_plugins/__pycache__/tifffile_plugin.cpython-310.pyc,,
skimage/io/_plugins/fits_plugin.ini,sha256=COnz8EogAEjsCtOJFsTGEsgeBWptWiEbcYmm7MIaZes,91
skimage/io/_plugins/fits_plugin.py,sha256=ftpfbtcWTIPLJCVKVDiAjkQ9ON0v6b3Ko8bKxCEUNIk,4542
skimage/io/_plugins/gdal_plugin.ini,sha256=j50edWdgwb7acEVHO4Qb7sX4ZNRlLBNWZbHI-9qhhK4,92
skimage/io/_plugins/gdal_plugin.py,sha256=Jcrof9Y2Lea6e9Zy8PP_nxoEe2x3tSwOBUbc7NMmjTk,366
skimage/io/_plugins/imageio_plugin.ini,sha256=lIKNERENcVlj25kL_FWDGpA5GuUjEOR7b64ivA4hLNo,91
skimage/io/_plugins/imageio_plugin.py,sha256=nn1OJZye2341We1uxwKoImSqlu58nIRXiTqv_V28jwY,344
skimage/io/_plugins/imread_plugin.ini,sha256=IB6IQ0YrAURcdZUVLIZdo27qQyIUdxXcSywi7iWdsLM,89
skimage/io/_plugins/imread_plugin.py,sha256=UL5HINTeOJqfXfn6lCnMhJxxmazwNWmGL_bVdqBjIQU,1002
skimage/io/_plugins/matplotlib_plugin.ini,sha256=5HSDnyPHYcK8KunSBSn4FJJKnidqxxtRGsngnQYRVCY,126
skimage/io/_plugins/matplotlib_plugin.py,sha256=kd6xSQkOxr2MslwwfdvzLjYkI3WP2bi7exunKfDvJjM,6687
skimage/io/_plugins/pil_plugin.ini,sha256=75uxuOky9BKlc0PF9w71zmp_ilVmeHx5UhZouuHCV48,94
skimage/io/_plugins/pil_plugin.py,sha256=3v7uxbIVnucYw6fCQ8NcO3IpWtQrHpWUch-u6g3LTgs,8104
skimage/io/_plugins/simpleitk_plugin.ini,sha256=e24xygfzDppazgLPAILifdt41t6QzayN47p9vPQKEkw,95
skimage/io/_plugins/simpleitk_plugin.py,sha256=Q9Yum18FO4dO0SvbuewJR7DPNZRQVfkZUnHjsNfzesM,554
skimage/io/_plugins/tifffile_plugin.ini,sha256=4nMhFJOanbVy1UDXNFMWBNXIA15l6yAb0IaZVwur-4g,113
skimage/io/_plugins/tifffile_plugin.py,sha256=ZXu3Z0tZ-VpKoJ5SsvuOBIT-Ljd66DxXCoOrZ5ozWQU,2145
skimage/io/collection.py,sha256=PiKbBcMEYLBcCuXwOCeTVRxFMoJhPHFH1_KAEkcmHjM,16449
skimage/io/manage_plugins.py,sha256=0T5n2UvDiROlCAp0TS6LhRcy7Fyl62KNHIRw8f6canY,12714
skimage/io/sift.py,sha256=dxdSi3-HbENhdOnZluJvcAeWDhOU4srBWC9GNxGAi9o,2654
skimage/io/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/io/tests/__pycache__/__init__.cpython-310.pyc,,
skimage/io/tests/__pycache__/test_collection.cpython-310.pyc,,
skimage/io/tests/__pycache__/test_fits.cpython-310.pyc,,
skimage/io/tests/__pycache__/test_imageio.cpython-310.pyc,,
skimage/io/tests/__pycache__/test_imread.cpython-310.pyc,,
skimage/io/tests/__pycache__/test_io.cpython-310.pyc,,
skimage/io/tests/__pycache__/test_mpl_imshow.cpython-310.pyc,,
skimage/io/tests/__pycache__/test_multi_image.cpython-310.pyc,,
skimage/io/tests/__pycache__/test_pil.cpython-310.pyc,,
skimage/io/tests/__pycache__/test_plugin.cpython-310.pyc,,
skimage/io/tests/__pycache__/test_sift.cpython-310.pyc,,
skimage/io/tests/__pycache__/test_simpleitk.cpython-310.pyc,,
skimage/io/tests/__pycache__/test_tifffile.cpython-310.pyc,,
skimage/io/tests/test_collection.py,sha256=zjQW2p1uwWRpD2BU7ihYdY7o4GCp6rwxMtyDUB2QaMg,5525
skimage/io/tests/test_fits.py,sha256=vAEVBO2_dBpzxPgcYXBvLdwLbHWY5IjnuuN9tf_lQtQ,919
skimage/io/tests/test_imageio.py,sha256=dmj5wBr6uXQqjxg-YHczqCf9FxAg77TXpPbIolA0JUc,2809
skimage/io/tests/test_imread.py,sha256=AgwfdE8h8fLvDLXaBLeL9IU_q6GgGRTI8fvr5VZbYiY,2021
skimage/io/tests/test_io.py,sha256=PJIw929x3VjBpMqkOC3BeR9QxYAJBH1X5Rbj80cJ0N0,5522
skimage/io/tests/test_mpl_imshow.py,sha256=hkFz1SIa4ZUA7a2kCa_XtZYo7m5JE0SGUj5cPG1mHGI,3871
skimage/io/tests/test_multi_image.py,sha256=bGHveKVWBrJu2v71ztWSkagGG5PsfIW-D_dyQfjcNYY,2627
skimage/io/tests/test_pil.py,sha256=e41tB3j4EP3JSTqSAYQ70Mq1-kztGKnMN5zr_vuORhc,9531
skimage/io/tests/test_plugin.py,sha256=Hq2wOlFpnx5_Z2urt6Xo8vH0f7eyPuJcgbo2WxZ-qyw,3107
skimage/io/tests/test_sift.py,sha256=SUzv608XiZKIZhLgBRBVuP0Q0U_4CvVPdBo0MwkxNww,3405
skimage/io/tests/test_simpleitk.py,sha256=GCzCouaNpbsJYID83LUfoc9SCTWCERiH-hoV2jqzSJ0,2642
skimage/io/tests/test_tifffile.py,sha256=PMgtI3DQDaTJOjqZvLVbwojJ4-J6oMD_odQtCakjMGw,2748
skimage/io/util.py,sha256=YphtIV3aawcS90aqdH2eqf6Uib4Aai2NgzEehY3PadY,1324
skimage/measure/__init__.py,sha256=_QbyA1AnfFDigdESNy5vwTc3vxYl6M2v3-dDAeD8UG8,179
skimage/measure/__init__.pyi,sha256=504CVQ2ni10kroObkwTJzZcJSl0fgA-YfF5AG7U_cys,1933
skimage/measure/__pycache__/__init__.cpython-310.pyc,,
skimage/measure/__pycache__/_blur_effect.cpython-310.pyc,,
skimage/measure/__pycache__/_colocalization.cpython-310.pyc,,
skimage/measure/__pycache__/_find_contours.cpython-310.pyc,,
skimage/measure/__pycache__/_label.cpython-310.pyc,,
skimage/measure/__pycache__/_marching_cubes_lewiner.cpython-310.pyc,,
skimage/measure/__pycache__/_marching_cubes_lewiner_luts.cpython-310.pyc,,
skimage/measure/__pycache__/_moments.cpython-310.pyc,,
skimage/measure/__pycache__/_moments_analytical.cpython-310.pyc,,
skimage/measure/__pycache__/_polygon.cpython-310.pyc,,
skimage/measure/__pycache__/_regionprops.cpython-310.pyc,,
skimage/measure/__pycache__/_regionprops_utils.cpython-310.pyc,,
skimage/measure/__pycache__/block.cpython-310.pyc,,
skimage/measure/__pycache__/entropy.cpython-310.pyc,,
skimage/measure/__pycache__/fit.cpython-310.pyc,,
skimage/measure/__pycache__/pnpoly.cpython-310.pyc,,
skimage/measure/__pycache__/profile.cpython-310.pyc,,
skimage/measure/_blur_effect.py,sha256=MIhj8yM4ATSaaO131Je3N9ckprBJ_KW6CVpI6CkR50o,3422
skimage/measure/_ccomp.cp310-win_amd64.lib,sha256=soVHzJn58_eKADYZJp20hNKimeQS5lNnFYDyadt6-yw,1582
skimage/measure/_ccomp.cp310-win_amd64.pyd,sha256=y9UZQqWhfWlYWExSh7VIV3t_HnMnttitNTsN3PqktBY,92160
skimage/measure/_colocalization.py,sha256=dt0fWUvD1zw-vlQgCydnlKgya3lh4zre6ga0W8KxB-0,12537
skimage/measure/_find_contours.py,sha256=fvgbNBAXcrUeJLIWaGN7eGUDB8EOCAK_FQQMiP5pooY,9803
skimage/measure/_find_contours_cy.cp310-win_amd64.lib,sha256=ytFrNGgweuKOI8HfxbY9t_5yW2kv9E6gqijU5DFBGRM,1746
skimage/measure/_find_contours_cy.cp310-win_amd64.pyd,sha256=L2r_rLLmY_t9PMZb3wCajfjmoMEQR-42aWSHXRX3VfE,144384
skimage/measure/_label.py,sha256=MV9dEjUzaRdxpK617ChFZJM8YDWqnfIH1Q-EIigfqQM,4085
skimage/measure/_marching_cubes_lewiner.py,sha256=3kWLe3fvb-D5gwEFvUbu_VSe6NQQey1PBkI_Rdot_BI,13208
skimage/measure/_marching_cubes_lewiner_cy.cp310-win_amd64.lib,sha256=C7mU1cIv-18fXi-Q9YibAXJGYGeNQNfbTqKPrAdK3kI,1882
skimage/measure/_marching_cubes_lewiner_cy.cp310-win_amd64.pyd,sha256=ZZbrnnL1-rOHTCmh8mHBD7bWQ5CLKpMvtuKcjrTzXUw,237056
skimage/measure/_marching_cubes_lewiner_luts.py,sha256=_ldIZmVhHAVSRwQE6WJRUI6C3ov1bKsYyIvEi-WWDXE,28474
skimage/measure/_moments.py,sha256=KRgAEDOV54EW4-DbJq8Nld4ARetQeWM0HZY8SJPzcWg,18352
skimage/measure/_moments_analytical.py,sha256=gOAChhiSqoesvDlp9-5WGtHLOI7H5qlktqNs3tSyvxs,6734
skimage/measure/_moments_cy.cp310-win_amd64.lib,sha256=iFlHqP0UfalWh6DMpbg65P3uF5QTNFPI3Iq36NFOyEY,1656
skimage/measure/_moments_cy.cp310-win_amd64.pyd,sha256=gYKzpgrrsgT7WiBDsGj0jbpDI-9jYvuhL_0Eq1kx2D8,163328
skimage/measure/_pnpoly.cp310-win_amd64.lib,sha256=FPTlMWoZ9k29J6LbYQh7eMhFnFbb8cuzKhS4OkH5a2g,1596
skimage/measure/_pnpoly.cp310-win_amd64.pyd,sha256=3QHKFKEBTMVq_lcQ4Ey5XX1ruPKF9l5EIXsUk2u0ngc,152064
skimage/measure/_polygon.py,sha256=PeaS0gCz_Plzj0RXdNo5QsKvkifJLvWJ8U3JKwz1kYU,5436
skimage/measure/_regionprops.py,sha256=IPew_UbL0WrJ0tmBAeH_oXCX7riu_eTRMNJYF54O-uo,53167
skimage/measure/_regionprops_utils.py,sha256=8PMCf0SJB819-YWxKRNGwcjVdw0txzvBxrgAJRI6QYc,16619
skimage/measure/block.py,sha256=HfCnXqafv1NRyDE2O0g73A0Dc3jKj52VYpmTZ_Lgb14,3355
skimage/measure/entropy.py,sha256=GEX-dDtFWMC-4aFaXz-njy8P4DaoDTcdAuVRtkr-Mkg,1185
skimage/measure/fit.py,sha256=0orrLpVqi-xjSjxBiyFQrA467n4rq2OfCOULxiID_Fg,33435
skimage/measure/pnpoly.py,sha256=cf9s4RSews7ZTQEcC55M5kjFF-UJpLhN4NOF2_kfTB0,2090
skimage/measure/profile.py,sha256=qBUvpyNs-jp7tkrOI7hCjL_H_K4lLtzT8B3nIWDoQtE,6971
skimage/measure/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/measure/tests/__pycache__/__init__.cpython-310.pyc,,
skimage/measure/tests/__pycache__/test_block.cpython-310.pyc,,
skimage/measure/tests/__pycache__/test_blur_effect.cpython-310.pyc,,
skimage/measure/tests/__pycache__/test_ccomp.cpython-310.pyc,,
skimage/measure/tests/__pycache__/test_colocalization.cpython-310.pyc,,
skimage/measure/tests/__pycache__/test_entropy.cpython-310.pyc,,
skimage/measure/tests/__pycache__/test_find_contours.cpython-310.pyc,,
skimage/measure/tests/__pycache__/test_fit.cpython-310.pyc,,
skimage/measure/tests/__pycache__/test_label.cpython-310.pyc,,
skimage/measure/tests/__pycache__/test_marching_cubes.cpython-310.pyc,,
skimage/measure/tests/__pycache__/test_moments.cpython-310.pyc,,
skimage/measure/tests/__pycache__/test_pnpoly.cpython-310.pyc,,
skimage/measure/tests/__pycache__/test_polygon.cpython-310.pyc,,
skimage/measure/tests/__pycache__/test_profile.cpython-310.pyc,,
skimage/measure/tests/__pycache__/test_regionprops.cpython-310.pyc,,
skimage/measure/tests/test_block.py,sha256=fubTmv2lHJWYVgzCAjKazMxkxlzMPzBFW_FN9Rhk5Nw,4152
skimage/measure/tests/test_blur_effect.py,sha256=jexontJNFodBmVn7HXPhNdGp6VvZG7W82RfN3we8lG8,2573
skimage/measure/tests/test_ccomp.py,sha256=Nw0bRDkIKgJFHqXcFtqb5YE8YYAHoRbL8z9LXMr60Qc,8285
skimage/measure/tests/test_colocalization.py,sha256=tDvoDYnyHujeTxVYxcsDVR1Q343dyUQ8X0nSjqXvSFM,4782
skimage/measure/tests/test_entropy.py,sha256=hpC3ilAoeWlmJtyz1Hf5cWzuVXUlR2ywN8gc_QZZgHQ,416
skimage/measure/tests/test_find_contours.py,sha256=Ae-5qinz7jLbQQrYkEEO4OI9q6EgAgPTzcfzifvwTH4,5457
skimage/measure/tests/test_fit.py,sha256=XP7a-2jGzaZ5rmhLsYWpVKIjZRCgsINT45lNALlcePM,22809
skimage/measure/tests/test_label.py,sha256=N-BxUvx_-vAg7_4ifnhMfIch_xl02z_sQ8c8kl-jWeY,1842
skimage/measure/tests/test_marching_cubes.py,sha256=fC1oTiEJc3Nz73Ecp5CgerboHv_mIEVx4tMVozlO1G4,7239
skimage/measure/tests/test_moments.py,sha256=7S41__Z1hT0P64ySVCCb1BM6DXah6ZNkpT_3NcQ13uA,12128
skimage/measure/tests/test_pnpoly.py,sha256=N5ZK_loCjByiP1xLJI-0ovsm-52ffTzife5hSjNs3GQ,1283
skimage/measure/tests/test_polygon.py,sha256=m6l1uTgMFDaVh1cQ0eHr086apPuEUXkhCudHYepFsFo,2367
skimage/measure/tests/test_profile.py,sha256=8_VWtKQUWpwrW8NSIKcoWTiBLQTbDZ1nOkic6wNrwMw,8106
skimage/measure/tests/test_regionprops.py,sha256=M4TrLB9G0dmwOYAlEw4Tj9MMqt0XfAP2pg4b28ra1jU,54618
skimage/metrics/__init__.py,sha256=-GZA-9MZsZGoiMafP_eqvJWYXBd2tUC_1t1B9lJWajc,185
skimage/metrics/__init__.pyi,sha256=rEWUju82JKOaUj-vVsbwlnF8hgP9J_9LK6NG8ijfvBU,914
skimage/metrics/__pycache__/__init__.cpython-310.pyc,,
skimage/metrics/__pycache__/_adapted_rand_error.cpython-310.pyc,,
skimage/metrics/__pycache__/_contingency_table.cpython-310.pyc,,
skimage/metrics/__pycache__/_structural_similarity.cpython-310.pyc,,
skimage/metrics/__pycache__/_variation_of_information.cpython-310.pyc,,
skimage/metrics/__pycache__/set_metrics.cpython-310.pyc,,
skimage/metrics/__pycache__/simple_metrics.cpython-310.pyc,,
skimage/metrics/_adapted_rand_error.py,sha256=_bJwIM6C7FsSdeztroqdeSV2ZVTp7bkeYdj3uWEKaLc,3674
skimage/metrics/_contingency_table.py,sha256=Aa1c-0sSmWlx274pTuAEk-5_SlFnQmJ5vO_57F8fwp4,1785
skimage/metrics/_structural_similarity.py,sha256=stlMm5Hjywz3U6VCumibZdQH5sSEEpCY5POXP3hTDxQ,10714
skimage/metrics/_variation_of_information.py,sha256=UT8cN9s-9TPDIMh4mWFz-4UqGNgBfSlILhehq4mN0bU,4387
skimage/metrics/set_metrics.py,sha256=eKyFUUW_BEUMOkYaQ0SD2yZKT22Jgq2FDl-vxBjOtCU,5043
skimage/metrics/simple_metrics.py,sha256=PVr3nIh2egef9FLFV06J3wQ3Jxw0WCzQPIzpJ1Ot_W0,8517
skimage/metrics/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/metrics/tests/__pycache__/__init__.cpython-310.pyc,,
skimage/metrics/tests/__pycache__/test_segmentation_metrics.cpython-310.pyc,,
skimage/metrics/tests/__pycache__/test_set_metrics.cpython-310.pyc,,
skimage/metrics/tests/__pycache__/test_simple_metrics.cpython-310.pyc,,
skimage/metrics/tests/__pycache__/test_structural_similarity.cpython-310.pyc,,
skimage/metrics/tests/test_segmentation_metrics.py,sha256=5aAR49IBnl1tgDrv8mBABLn3Zrj8uGeS4nvd3Q9ep-I,2674
skimage/metrics/tests/test_set_metrics.py,sha256=IdGBV0kqdbh8A_50pqQCcVK3z8wHoO--HATI4LZ355Y,7109
skimage/metrics/tests/test_simple_metrics.py,sha256=SuCJOfwkwTzkdNh1VFKd9uVdPSus1TtMecZ_hrZFPnc,4976
skimage/metrics/tests/test_structural_similarity.py,sha256=pfbV4hYzvH9XV-z_FXpwfLQGXtA-jLFeptKdHJ6Xkns,10023
skimage/morphology/__init__.py,sha256=MT4o_n0Erc2u5GuXkx6-KZKLEFemSTdH9J9RiRGRIk8,2208
skimage/morphology/__pycache__/__init__.cpython-310.pyc,,
skimage/morphology/__pycache__/_flood_fill.cpython-310.pyc,,
skimage/morphology/__pycache__/_skeletonize.cpython-310.pyc,,
skimage/morphology/__pycache__/_util.cpython-310.pyc,,
skimage/morphology/__pycache__/binary.cpython-310.pyc,,
skimage/morphology/__pycache__/convex_hull.cpython-310.pyc,,
skimage/morphology/__pycache__/extrema.cpython-310.pyc,,
skimage/morphology/__pycache__/footprints.cpython-310.pyc,,
skimage/morphology/__pycache__/gray.cpython-310.pyc,,
skimage/morphology/__pycache__/grayreconstruct.cpython-310.pyc,,
skimage/morphology/__pycache__/isotropic.cpython-310.pyc,,
skimage/morphology/__pycache__/max_tree.cpython-310.pyc,,
skimage/morphology/__pycache__/misc.cpython-310.pyc,,
skimage/morphology/_convex_hull.cp310-win_amd64.lib,sha256=loo87lEN94aGia4xIRip3XwXzhW-da-z1TVFOOT6xSo,1672
skimage/morphology/_convex_hull.cp310-win_amd64.pyd,sha256=R22mjb_psMsucHF9JMEy2gXYR7gV-HVnFP7XhTA4k9o,138752
skimage/morphology/_extrema_cy.cp310-win_amd64.lib,sha256=q5JsGHd6bUrMpOMd1Zfn9Mk0z0QNbnU4upTQzW9vNH8,1656
skimage/morphology/_extrema_cy.cp310-win_amd64.pyd,sha256=zuY1Dlqb826JJkkrxz6JNWZPppa4NbuSJuaSN3ztr3w,217088
skimage/morphology/_flood_fill.py,sha256=5P9wcTuCWD6AbO4v8afObKJ3bqwaGMqmwL6dUXj76DU,11043
skimage/morphology/_flood_fill_cy.cp310-win_amd64.lib,sha256=XjE2i4u4S1ZJsf_PPSrvd_LrBR4DfLR8fc67VStUMfU,1702
skimage/morphology/_flood_fill_cy.cp310-win_amd64.pyd,sha256=RV1StUxWCyay9aosizBj2OMVLmoI-WQN5aDnMAKGwhU,258560
skimage/morphology/_grayreconstruct.cp310-win_amd64.lib,sha256=RpccgEByBJ6oJFvaULkNmdWwJtay-XB8Jpdrk37_8dw,1732
skimage/morphology/_grayreconstruct.cp310-win_amd64.pyd,sha256=i3MRsPEZvU_9a1Z58AC5bqBc-AMj3ZpLk4w2k2XRa8E,178176
skimage/morphology/_max_tree.cp310-win_amd64.lib,sha256=S6Lp-mDiQqbSzSLiRJB4NOyzBm-E5SHLCWSr7SX_JS4,1626
skimage/morphology/_max_tree.cp310-win_amd64.pyd,sha256=cEOH3Mf8haHzTnRgx38xI-zrapGFeiGPnhuhNuD73n4,600576
skimage/morphology/_misc_cy.cp310-win_amd64.lib,sha256=KiHwzSVQeNr7Zi1iPYlrSD2AC4S4QJFu9iTb3SBMthc,1612
skimage/morphology/_misc_cy.cp310-win_amd64.pyd,sha256=u4Xs5CHv20WXBCB8AdbSMfqIzMFznsCCus4totzyysc,209408
skimage/morphology/_skeletonize.py,sha256=kMzKUyfGE8rq6ifyBbbZYXjSfpRJx9LuqTuDe9R68Lo,24087
skimage/morphology/_skeletonize_lee_cy.cp310-win_amd64.lib,sha256=XBT61yHTzmlVXnRq8TRiG8jnEKpH87OcRdEaytZmI3A,1776
skimage/morphology/_skeletonize_lee_cy.cp310-win_amd64.pyd,sha256=lxJqguK3tzOrMMYBQZ2OnZKYKSwZc0TxzxDqS_GSy-I,156672
skimage/morphology/_skeletonize_various_cy.cp310-win_amd64.lib,sha256=asfSpQVmPbl0kKPRj8Dl_960ZiPN-cbwSrOxg--u114,1836
skimage/morphology/_skeletonize_various_cy.cp310-win_amd64.pyd,sha256=wVuELMAWfCNg991y3NIXf5Te9Dpme_JA_JVcH7xM11Q,155648
skimage/morphology/_util.py,sha256=DgVeNs9w8bAvxqc2xfNYFG5DZAr-Hj5MLU-emocW19w,12347
skimage/morphology/ball_decompositions.npy,sha256=T561HzYf19fSLTQt7Hu5gXiiGqnJRFB_JYmLjKIT5U0,431
skimage/morphology/binary.py,sha256=_LZoX3FD_1sflbNCB0fulzNhnuvP6DO086EjOWBwcCo,12432
skimage/morphology/convex_hull.py,sha256=QNLuxgpQV-FzSTE0sv87y2JoswsMoQo8kd6WM7UZg3g,8641
skimage/morphology/disk_decompositions.npy,sha256=dpc557PYxwYZk2gs30WgwEiUCVL7eOMbFy2DD3545-4,881
skimage/morphology/extrema.py,sha256=p2OBY191lIpNA_x0ol3DI9WuBoF8oV_5aSgA_j8DNvA,21365
skimage/morphology/footprints.py,sha256=EjFQ_GuXuRp4XaEE33wyUyZaQR1iG0cB5CWWRbGzLJo,43846
skimage/morphology/gray.py,sha256=EGHAXKJmk2t22lyY26pcgpXhR8-3V4atw02kyhztpiA,26631
skimage/morphology/grayreconstruct.py,sha256=LuJK294Tt1qRhHg1jQzvbC1xZUl84B16uCQC-CV0LdU,9567
skimage/morphology/isotropic.py,sha256=pPQ3zFtHRFGXtxFVnJ70Bl69HbKvW3kmdUll-LdpCXs,8073
skimage/morphology/max_tree.py,sha256=3Ria7Pp6jH-0qVGPaPVjnaWiRtJ_6jl09XKmZRyqIoI,27683
skimage/morphology/misc.py,sha256=jitRY7NoF9otDhB2gsdjSi-D4QirWkvSEzzC3UPgX8Q,17135
skimage/morphology/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/morphology/tests/__pycache__/__init__.cpython-310.pyc,,
skimage/morphology/tests/__pycache__/test_binary.cpython-310.pyc,,
skimage/morphology/tests/__pycache__/test_convex_hull.cpython-310.pyc,,
skimage/morphology/tests/__pycache__/test_extrema.cpython-310.pyc,,
skimage/morphology/tests/__pycache__/test_flood_fill.cpython-310.pyc,,
skimage/morphology/tests/__pycache__/test_footprints.cpython-310.pyc,,
skimage/morphology/tests/__pycache__/test_gray.cpython-310.pyc,,
skimage/morphology/tests/__pycache__/test_isotropic.cpython-310.pyc,,
skimage/morphology/tests/__pycache__/test_max_tree.cpython-310.pyc,,
skimage/morphology/tests/__pycache__/test_misc.cpython-310.pyc,,
skimage/morphology/tests/__pycache__/test_reconstruction.cpython-310.pyc,,
skimage/morphology/tests/__pycache__/test_skeletonize.cpython-310.pyc,,
skimage/morphology/tests/__pycache__/test_util.cpython-310.pyc,,
skimage/morphology/tests/test_binary.py,sha256=aDDselZqIkEiTG58gIC8RED0TDSqJnxHITtJ950UArI,12360
skimage/morphology/tests/test_convex_hull.py,sha256=bUnvU-8htkipUHWoMiEXTiNPuRmcHCEKnE2nPu_6kgI,9209
skimage/morphology/tests/test_extrema.py,sha256=pn11FA2WOwNUCnJt1U8Nqk1VSXxaoqQDE7mg-I0d_Pw,27617
skimage/morphology/tests/test_flood_fill.py,sha256=Z9Ufwfflvnz0bNTkVmiW39pskJKM0JdkKXmAoRBjvL8,9778
skimage/morphology/tests/test_footprints.py,sha256=Yw7PkJ2lmiVcJOPyPGrs8e4Pd3sEvP06jyp8YpUIcUA,12056
skimage/morphology/tests/test_gray.py,sha256=Sq_vt3EaEMHgkcigbvLhx52Wgf7bdepAHBYnNUyW2eM,16840
skimage/morphology/tests/test_isotropic.py,sha256=Ve6jr8KSjWnwmqkK362I88ccCLQQnRv7ImerJHeXvRs,2928
skimage/morphology/tests/test_max_tree.py,sha256=YEKAAJdDmXk1CebeOq5epCIMWBxKlHe-scrz7jBpbdI,21080
skimage/morphology/tests/test_misc.py,sha256=XHUhc8_PvtxCXia_090-0VDdgbZHK0Wegq8yXii_Gd0,18529
skimage/morphology/tests/test_reconstruction.py,sha256=tjmwfnT0M_zHIu4zjSdcKC-oLosgO2a0NS9NHMnwnAo,6058
skimage/morphology/tests/test_skeletonize.py,sha256=PuxU8P-wRSefJjWAqOU8oQ-6-sDA7OSLq0T12jPBxhk,14048
skimage/morphology/tests/test_util.py,sha256=RxMxg08ia7TlvExuM8EJ8rfmgnqFyHeWXkd9NWruPnY,5881
skimage/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/registration/__init__.py,sha256=JiN_TinNfTq4MI9b3Hzh_QcNnobGunYFDlTrfDXWTJ0,189
skimage/registration/__init__.pyi,sha256=g8uGNCEO2j6Ycadm2iwQMTCQbpvUSv9GVrRUS2myYLw,374
skimage/registration/__pycache__/__init__.cpython-310.pyc,,
skimage/registration/__pycache__/_masked_phase_cross_correlation.cpython-310.pyc,,
skimage/registration/__pycache__/_optical_flow.cpython-310.pyc,,
skimage/registration/__pycache__/_optical_flow_utils.cpython-310.pyc,,
skimage/registration/__pycache__/_phase_cross_correlation.cpython-310.pyc,,
skimage/registration/_masked_phase_cross_correlation.py,sha256=BigH4m9EEQL0p-ZrZ9YTirNuJ9u0jbYi5YmOZKGH_30,12412
skimage/registration/_optical_flow.py,sha256=XX0iE1zHFL34qhx5TVuRjyWZGE_xFWz3ejOXVOWafLA,14976
skimage/registration/_optical_flow_utils.py,sha256=ZwmZzAkHDyThfXOC78cEl-5nEFtSgpYDryedj1Yvkn8,3830
skimage/registration/_phase_cross_correlation.py,sha256=QmhhPDPvoVPkfpA84bLkHbxrS4iCbRZRjyCKtqca6Kc,18287
skimage/registration/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/registration/tests/__pycache__/__init__.cpython-310.pyc,,
skimage/registration/tests/__pycache__/test_ilk.cpython-310.pyc,,
skimage/registration/tests/__pycache__/test_masked_phase_cross_correlation.cpython-310.pyc,,
skimage/registration/tests/__pycache__/test_phase_cross_correlation.cpython-310.pyc,,
skimage/registration/tests/__pycache__/test_tvl1.cpython-310.pyc,,
skimage/registration/tests/test_ilk.py,sha256=7f53abpJBvQfASyPmictP2bRKPh7RSHeVZ_gJlu4xn4,3197
skimage/registration/tests/test_masked_phase_cross_correlation.py,sha256=-QQxqNJhL3P9OqlO6Msk_vsCezXIlirMlA_TWw3ltLc,9824
skimage/registration/tests/test_phase_cross_correlation.py,sha256=eA11peV0PoGEoBnlzGjC7IFcjFzbQrEnc4EDI49Goo8,8628
skimage/registration/tests/test_tvl1.py,sha256=lisdQmzMbrGv-qeEJZ-4tDCIe93Ae9ZBFSjahnARr1Q,3682
skimage/restoration/__init__.py,sha256=M9nPtW5iQZ3BnI7ydV7YqTkrVfAD4MiBUkT5qislLhY,183
skimage/restoration/__init__.pyi,sha256=eBsIh0wzNNmqHRnnDUNF23DTorGjSAs5dxFDA24WA9Q,1105
skimage/restoration/__pycache__/__init__.cpython-310.pyc,,
skimage/restoration/__pycache__/_cycle_spin.cpython-310.pyc,,
skimage/restoration/__pycache__/_denoise.cpython-310.pyc,,
skimage/restoration/__pycache__/_rolling_ball.cpython-310.pyc,,
skimage/restoration/__pycache__/deconvolution.cpython-310.pyc,,
skimage/restoration/__pycache__/inpaint.cpython-310.pyc,,
skimage/restoration/__pycache__/j_invariant.cpython-310.pyc,,
skimage/restoration/__pycache__/non_local_means.cpython-310.pyc,,
skimage/restoration/__pycache__/uft.cpython-310.pyc,,
skimage/restoration/__pycache__/unwrap.cpython-310.pyc,,
skimage/restoration/_cycle_spin.py,sha256=XL6tcd2H_ixAA1_5RCjGkovhq0WSUCvjMBR4JaIeFaw,6049
skimage/restoration/_denoise.py,sha256=X5_Q1tFaXfoV7VoX_xQW2ni8qQa-4E9jNaYBllLkEPs,42531
skimage/restoration/_denoise_cy.cp310-win_amd64.lib,sha256=HUdmAp2wOBC_F-4fp26WIKme0EUNss9PmUynbVsJy78,1656
skimage/restoration/_denoise_cy.cp310-win_amd64.pyd,sha256=-05TAhRnYM_VkYuNrkZGWfTWI1FhTtizwindLffsUaA,221696
skimage/restoration/_inpaint.cp310-win_amd64.lib,sha256=RTDpLVRHQl3_dcjc9EJR9vNBv_nyaJWUN4OgB110mgs,1612
skimage/restoration/_inpaint.cp310-win_amd64.pyd,sha256=xmiCIsZoJHqgPZ_Oo7iX8ZTX-CEYDO6Wtw5f237ylg0,173568
skimage/restoration/_nl_means_denoising.cp310-win_amd64.lib,sha256=7NhMC_ox7RGCJBTRdLfbPqiUCoO14oM1rVvXfAGWFOg,1776
skimage/restoration/_nl_means_denoising.cp310-win_amd64.pyd,sha256=gQGQqIiGKgWce3WkKx1jGZN0aTLeMRHCcHWgP5mgDJo,409600
skimage/restoration/_rolling_ball.py,sha256=s67b9lnoKZPDJ3pGzdn1VsyTkQdYHJqrTeHjViok5Vs,7049
skimage/restoration/_rolling_ball_cy.cp310-win_amd64.lib,sha256=YLCrbf9aDQkqrGfARc4h2DRoKcWsWePmr_9qGooWHjo,1732
skimage/restoration/_rolling_ball_cy.cp310-win_amd64.pyd,sha256=yYGeEiv_OaLt3zhM4gry94pBfNzwkDB9oMeGM64LPqk,206848
skimage/restoration/_unwrap_1d.cp310-win_amd64.lib,sha256=Vg9cZKJ_jl8__T29MmqMYLxhO_R3PYC8YROm6OAmJf4,1642
skimage/restoration/_unwrap_1d.cp310-win_amd64.pyd,sha256=D37F7vE-VuRckb0uufhECmYMYKvEonyRO2Z9pPiSpQM,133632
skimage/restoration/_unwrap_2d.cp310-win_amd64.lib,sha256=FIaKpIFl21dwZh26eDM6MgTbBuXdEcnpT32nlhcPIJU,1642
skimage/restoration/_unwrap_2d.cp310-win_amd64.pyd,sha256=YbESCWwHM2xpH__IfV-aGQ4vChYczb6A5_q7BGVxXlU,144896
skimage/restoration/_unwrap_3d.cp310-win_amd64.lib,sha256=DFiEHgGQvEJMRUuFhRyihUcL7RYuM6A2AlbhAiYcsr4,1642
skimage/restoration/_unwrap_3d.cp310-win_amd64.pyd,sha256=6gaU08iKpmneUZzXGOy6QLPxxbC2WX_wDYZnelQ-Yvw,160768
skimage/restoration/deconvolution.py,sha256=N9y2JoW9s5bDtTQQZwa1VKO-zlisWLn_3qvU95Rh__g,16456
skimage/restoration/inpaint.py,sha256=ISZH0XF0qU82atuNYBZCaQtGwT47Ox0__mXjDUCN2fM,12997
skimage/restoration/j_invariant.py,sha256=WNHlE1o0mb7HQolVq_sPChXloLdvZSu4vYfqMm5mj10,12760
skimage/restoration/non_local_means.py,sha256=Hwm1HlEzg6gaMxgudtdlKY6C6rawwWRVvVaH4L20Szs,7905
skimage/restoration/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/restoration/tests/__pycache__/__init__.cpython-310.pyc,,
skimage/restoration/tests/__pycache__/test_denoise.cpython-310.pyc,,
skimage/restoration/tests/__pycache__/test_inpaint.cpython-310.pyc,,
skimage/restoration/tests/__pycache__/test_j_invariant.cpython-310.pyc,,
skimage/restoration/tests/__pycache__/test_restoration.cpython-310.pyc,,
skimage/restoration/tests/__pycache__/test_rolling_ball.cpython-310.pyc,,
skimage/restoration/tests/__pycache__/test_unwrap.cpython-310.pyc,,
skimage/restoration/tests/test_denoise.py,sha256=eqmBQWxAjnbzeZ0XC-zs1qEs5OFLu4V-UVEzbm2UZCM,41563
skimage/restoration/tests/test_inpaint.py,sha256=WoWFsC-aXyGt2T9VIR9jgBUm8h4VEUmuqS1yqJ5GWqM,7039
skimage/restoration/tests/test_j_invariant.py,sha256=iRTWPyhBXcEYb8Nr6rk4hXoLP6s8Acn5NNtTks6GZow,3371
skimage/restoration/tests/test_restoration.py,sha256=cKJPReAtQu2RjGJjIjXf0lTAjq65tdhClKF6B_2_GDE,6633
skimage/restoration/tests/test_rolling_ball.py,sha256=YNFxwq5OoG9OrlTbDbBbSDFQM5W8p40Gf0zHDi5jb88,3167
skimage/restoration/tests/test_unwrap.py,sha256=WrAxneWmfecP0g-Vsy7bg_RT_NAfcHg67NTkajfHqac,8583
skimage/restoration/uft.py,sha256=Q_Jv1QGRIUzUIYY7Zw_87KvGR9SwVhVU4BR7mFxwGW8,13064
skimage/restoration/unwrap.py,sha256=BcOFtOhbUwJT5TOIrBhgPb4qCcGl-udW2ijXtekL6OE,4946
skimage/segmentation/__init__.py,sha256=vo_AxOQjwpXnJL1tVsLkQczOdZSRiFD-FNuC-Pn3j3Y,1298
skimage/segmentation/__pycache__/__init__.cpython-310.pyc,,
skimage/segmentation/__pycache__/_chan_vese.cpython-310.pyc,,
skimage/segmentation/__pycache__/_clear_border.cpython-310.pyc,,
skimage/segmentation/__pycache__/_expand_labels.cpython-310.pyc,,
skimage/segmentation/__pycache__/_felzenszwalb.cpython-310.pyc,,
skimage/segmentation/__pycache__/_join.cpython-310.pyc,,
skimage/segmentation/__pycache__/_quickshift.cpython-310.pyc,,
skimage/segmentation/__pycache__/_watershed.cpython-310.pyc,,
skimage/segmentation/__pycache__/active_contour_model.cpython-310.pyc,,
skimage/segmentation/__pycache__/boundaries.cpython-310.pyc,,
skimage/segmentation/__pycache__/morphsnakes.cpython-310.pyc,,
skimage/segmentation/__pycache__/random_walker_segmentation.cpython-310.pyc,,
skimage/segmentation/__pycache__/slic_superpixels.cpython-310.pyc,,
skimage/segmentation/_chan_vese.py,sha256=r25UBhMQDRBgcK4QK1tW_jlvFAE6p2uBEL9pjhqDrUQ,14139
skimage/segmentation/_clear_border.py,sha256=2knKb2_zdUATKnhN29LBOZoyT7TZrdSElcS0mXOzLpo,4098
skimage/segmentation/_expand_labels.py,sha256=9RfSNdjPla7D_MaxsO-fbq42z__rFJxHfx7ZW-q8QEQ,4305
skimage/segmentation/_felzenszwalb.py,sha256=AL4nSKnDetCgYTz3rL66MHVWwMG8L0L798o73WBJ7FY,2555
skimage/segmentation/_felzenszwalb_cy.cp310-win_amd64.lib,sha256=AlG6d_9j3qcxRwXEzoh9EAQHwpKKpzKIC8FDppT3rcA,1732
skimage/segmentation/_felzenszwalb_cy.cp310-win_amd64.pyd,sha256=h2RfGYR0CEyaUbiBh9CQyMdnED-WVGezcRBeOjWNvMU,103424
skimage/segmentation/_join.py,sha256=vgyLcn1XQWj_SaTNVbnLnpln8M8NDH5xaUlUQctkOf8,7318
skimage/segmentation/_quickshift.py,sha256=Pg7QCGmNWZHSvyfukR23AVTZeyKanrqGBB1d-WivdMU,3573
skimage/segmentation/_quickshift_cy.cp310-win_amd64.lib,sha256=dJi3VlH66tKsX74HhtVrCh395Bq6ae6ckbu7XIoZSnk,1702
skimage/segmentation/_quickshift_cy.cp310-win_amd64.pyd,sha256=67sUuOuA3D2qDMND3VC7f8Owh-znlUrtjYtMFHw5l0k,198656
skimage/segmentation/_slic.cp310-win_amd64.lib,sha256=nOGRt_mNfH9mMo-IyhQe7-tFFV8hbJVwwrfLalghakg,1566
skimage/segmentation/_slic.cp310-win_amd64.pyd,sha256=x6wQK28um2WflWdtYKaYglRm2_JiHvZXSu65BqHzuds,217600
skimage/segmentation/_watershed.py,sha256=794zZUV318HOjdi-udTMjngAHX0llLFVb86QD3xIiWM,10055
skimage/segmentation/_watershed_cy.cp310-win_amd64.lib,sha256=e6rpGyuJfFatvlNY_ahV7a6qUsFVRA2fMepK7Re3kJw,1686
skimage/segmentation/_watershed_cy.cp310-win_amd64.pyd,sha256=l4ma5F_eid8O6G6u1mmvocYeZwpDIfURCKZEO3F21g4,211968
skimage/segmentation/active_contour_model.py,sha256=1SJ3yZumqqguT-HXh7ZUHWzMb2-2yHeNuHsnYPJT0cY,8089
skimage/segmentation/boundaries.py,sha256=kSdTJ2mCympOGZENDTDS1hv9av-oRII89mNSooM8glI,10256
skimage/segmentation/morphsnakes.py,sha256=INy8KMHSUHpmORc_4bfhe0ltvhdWwLq8aLGEmcvlTps,15331
skimage/segmentation/random_walker_segmentation.py,sha256=dXuSWnHnWr-CC2AGYC6PEHuKJXCOA3tpqHc-SfINovo,22305
skimage/segmentation/slic_superpixels.py,sha256=DoViWoadlwDCXlWllZTN2vCv3D2dJhujFUgF6EbXRl4,16802
skimage/segmentation/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/segmentation/tests/__pycache__/__init__.cpython-310.pyc,,
skimage/segmentation/tests/__pycache__/test_active_contour_model.cpython-310.pyc,,
skimage/segmentation/tests/__pycache__/test_boundaries.cpython-310.pyc,,
skimage/segmentation/tests/__pycache__/test_chan_vese.cpython-310.pyc,,
skimage/segmentation/tests/__pycache__/test_clear_border.cpython-310.pyc,,
skimage/segmentation/tests/__pycache__/test_expand_labels.cpython-310.pyc,,
skimage/segmentation/tests/__pycache__/test_felzenszwalb.cpython-310.pyc,,
skimage/segmentation/tests/__pycache__/test_join.cpython-310.pyc,,
skimage/segmentation/tests/__pycache__/test_morphsnakes.cpython-310.pyc,,
skimage/segmentation/tests/__pycache__/test_quickshift.cpython-310.pyc,,
skimage/segmentation/tests/__pycache__/test_random_walker.cpython-310.pyc,,
skimage/segmentation/tests/__pycache__/test_slic.cpython-310.pyc,,
skimage/segmentation/tests/__pycache__/test_watershed.cpython-310.pyc,,
skimage/segmentation/tests/test_active_contour_model.py,sha256=s0KqDuQ1-wJ6AzmIKALKi86WOd6e6ljGDiRVbSroYSI,6068
skimage/segmentation/tests/test_boundaries.py,sha256=7-b_5_AKUWvhmO18DPy7MnOIockTVkiFABY3mXVju7c,5371
skimage/segmentation/tests/test_chan_vese.py,sha256=VqUQcZfxNuY8qiElqlYDypqQHQ_hR40ktjubFtnfY7s,3471
skimage/segmentation/tests/test_clear_border.py,sha256=6z3RDwTFkkIsZIfbMuvNz5LwTgIDs-7dCZ0mTl2mlQg,5681
skimage/segmentation/tests/test_expand_labels.py,sha256=68e8s40nd-1NlI0_KMtFcG15raoJj71FZq8KzIQQ8tw,6802
skimage/segmentation/tests/test_felzenszwalb.py,sha256=cUFYxYanH8iggwvBLVgkZ8lgHo83kFVwN6ZUY_Ql60Y,2943
skimage/segmentation/tests/test_join.py,sha256=2AqV4K__dKLeBRzJhJaLq142i6ierf_qkQSc1nU5RFg,7417
skimage/segmentation/tests/test_morphsnakes.py,sha256=PoAoyEAFIXmw1Kbo5UQOpuHulrDrWtVQUTfKybwO8gQ,4757
skimage/segmentation/tests/test_quickshift.py,sha256=Vo7XFJjUKHrlgzuiKVqC28smLCOO-R7BV0bmg5l4dmQ,2467
skimage/segmentation/tests/test_random_walker.py,sha256=Qu1WL3t3PZ5qgE5TgC__ye-8c047xQDVpzPD0j3AH0Q,22218
skimage/segmentation/tests/test_slic.py,sha256=GVj4wHoC0vawPPYQPSMc3GF9MDFE5PbnxB1I4Rm6dIY,19122
skimage/segmentation/tests/test_watershed.py,sha256=Yxw68IDcfFdkcsc-a9UoMFmllf94N43FWxklGzEnxGQ,34395
skimage/transform/__init__.py,sha256=W_OcS-s6AaA6pwIvADlbmxft5SgUj4Zh4OTbNL_ojdc,1530
skimage/transform/__init__.pyi,sha256=pY7KccFUtEodxfq_5DXyZyXDbpXXhd8d_ZBt1KmA0no,2067
skimage/transform/__pycache__/__init__.cpython-310.pyc,,
skimage/transform/__pycache__/_geometric.cpython-310.pyc,,
skimage/transform/__pycache__/_thin_plate_splines.cpython-310.pyc,,
skimage/transform/__pycache__/_warps.cpython-310.pyc,,
skimage/transform/__pycache__/finite_radon_transform.cpython-310.pyc,,
skimage/transform/__pycache__/hough_transform.cpython-310.pyc,,
skimage/transform/__pycache__/integral.cpython-310.pyc,,
skimage/transform/__pycache__/pyramids.cpython-310.pyc,,
skimage/transform/__pycache__/radon_transform.cpython-310.pyc,,
skimage/transform/_geometric.py,sha256=mvhJSoy0zIc_uELFCZCL2JZA7fa5qUcm7_kOlLt3mFk,60480
skimage/transform/_hough_transform.cp310-win_amd64.lib,sha256=WPocTnCjF2YVfhUnBx1fZIUo82LWmWuNX8TpHeXplsE,1732
skimage/transform/_hough_transform.cp310-win_amd64.pyd,sha256=cWAhoFPzwvNB6aMFIMMd227qqxVbC5-7KLI-kFQfnK8,218624
skimage/transform/_radon_transform.cp310-win_amd64.lib,sha256=7r9C25zvlv3cpeQDGE8_2WX8gJp81uOAIBPZ5dlY13g,1732
skimage/transform/_radon_transform.cp310-win_amd64.pyd,sha256=QBqYaTTAkVc3FOJ73rmEkZdk9YaPLIvYsZZtB--7DHQ,176640
skimage/transform/_thin_plate_splines.py,sha256=P3HMLWWCY9LfGSKYnoCTIkxSdtYvmRcjADpTgsffMNU,5989
skimage/transform/_warps.py,sha256=yTczvRTkANs_vFu1Xorz7M8d4su89lB43XwrQIxVrrE,49899
skimage/transform/_warps_cy.cp310-win_amd64.lib,sha256=EPUDr--wdPVANSytkYgsJZg28IkZWLDS-zB_T7evDCA,1626
skimage/transform/_warps_cy.cp310-win_amd64.pyd,sha256=0cY0l6w8eJqw4rTu4-akIoS5jEwMJW5ffpuZ9TunHdw,186880
skimage/transform/finite_radon_transform.py,sha256=b5FhIIKX_QgdS_Tc8OA6qtUSQF_8YuRnRRPWJwaRQ7s,3311
skimage/transform/hough_transform.py,sha256=My-DnS40EBanXJtfyXd9P3CYJpmkruhfuj8PBGPD6TA,16340
skimage/transform/integral.py,sha256=9jHMXVMy7Hqc-Pj1tmht7FJxKbJxBCvtKX6mGdYVRiw,5241
skimage/transform/pyramids.py,sha256=Hy43PBWJnr4DzI-CgEhHox1GL-4pahlRpb3v0YSqvrY,13765
skimage/transform/radon_transform.py,sha256=I4SOt-_Oxad0DLWOfINUhOZRk_kYvo1h7d9pWeH7n_g,21274
skimage/transform/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/transform/tests/__pycache__/__init__.cpython-310.pyc,,
skimage/transform/tests/__pycache__/test_finite_radon_transform.cpython-310.pyc,,
skimage/transform/tests/__pycache__/test_geometric.cpython-310.pyc,,
skimage/transform/tests/__pycache__/test_hough_transform.cpython-310.pyc,,
skimage/transform/tests/__pycache__/test_integral.cpython-310.pyc,,
skimage/transform/tests/__pycache__/test_pyramids.cpython-310.pyc,,
skimage/transform/tests/__pycache__/test_radon_transform.cpython-310.pyc,,
skimage/transform/tests/__pycache__/test_thin_plate_splines.cpython-310.pyc,,
skimage/transform/tests/__pycache__/test_warps.cpython-310.pyc,,
skimage/transform/tests/test_finite_radon_transform.py,sha256=ZNeiCPF_QpmfipLBeyHruQAgDVa_w2uOra9l-tgVVTA,329
skimage/transform/tests/test_geometric.py,sha256=v-bz-zzYAKecgjgLKIeP0h9IoloHiGde33NteIbP4gs,35556
skimage/transform/tests/test_hough_transform.py,sha256=ELq7XF1Oafanzli-XNNn2GVFBp-7-fhYUv1xIprTaZQ,19695
skimage/transform/tests/test_integral.py,sha256=Yi6zMlXu6po3Z6UqOzoDuVr8zJ__rplzL6dCxTS6noM,2405
skimage/transform/tests/test_pyramids.py,sha256=eXWGlGgHu08xg5jVH-58sedW370xSreaffjs9f4R0j4,8257
skimage/transform/tests/test_radon_transform.py,sha256=stPU31VE45aXPaFyDBqPuxyL8jhOK0szDqLnB_LPNPY,19158
skimage/transform/tests/test_thin_plate_splines.py,sha256=20YWGW2sqkJ3Fc-6EfYAq1D4cLhX3jrenZydNEY4phs,2819
skimage/transform/tests/test_warps.py,sha256=YPKyABfT7jZSEMyzNWwuKhB9rw1jgKF9IOEQw9ZLRbk,34054
skimage/util/__init__.py,sha256=Vsw0eoC37V00iqNL54IyS-ezESWyPJEGo_p5U97QwWY,1388
skimage/util/__pycache__/__init__.cpython-310.pyc,,
skimage/util/__pycache__/_invert.cpython-310.pyc,,
skimage/util/__pycache__/_label.cpython-310.pyc,,
skimage/util/__pycache__/_map_array.cpython-310.pyc,,
skimage/util/__pycache__/_montage.cpython-310.pyc,,
skimage/util/__pycache__/_regular_grid.cpython-310.pyc,,
skimage/util/__pycache__/_slice_along_axes.cpython-310.pyc,,
skimage/util/__pycache__/apply_parallel.cpython-310.pyc,,
skimage/util/__pycache__/arraycrop.cpython-310.pyc,,
skimage/util/__pycache__/compare.cpython-310.pyc,,
skimage/util/__pycache__/dtype.cpython-310.pyc,,
skimage/util/__pycache__/lookfor.cpython-310.pyc,,
skimage/util/__pycache__/noise.cpython-310.pyc,,
skimage/util/__pycache__/shape.cpython-310.pyc,,
skimage/util/__pycache__/unique.cpython-310.pyc,,
skimage/util/_invert.py,sha256=T5-15Tcgo4lr_vKiS_t1VzellymN2FKHoFyBkRMr5pg,2634
skimage/util/_label.py,sha256=JMW7-dBnJONNM5RdcfvUNveK64w-7RA-4kXPuitMCCw,1619
skimage/util/_map_array.py,sha256=PMK1es-pmUmopAfkU1AGFvDCOogUPNpxl19_wBXiiRA,6894
skimage/util/_montage.py,sha256=fRTJjP8b4cYzSBuoxjNBRurXBtw10UrwvsCqVWjDe5E,4937
skimage/util/_regular_grid.py,sha256=NNvI2uEAVoQ25oDE-mlawcslBnsUGrMq7UodmnJc0Ok,4003
skimage/util/_remap.cp310-win_amd64.lib,sha256=ngE8fOldoIEmNAEak7lLA_AnRH1DRKJXRH_H3dG2UJw,1582
skimage/util/_remap.cp310-win_amd64.pyd,sha256=n6CdRDCzVT-h11Sb89aIQGA88yq9VPm92budaYiD1-Y,530432
skimage/util/_slice_along_axes.py,sha256=uIWtJpcPIO_6WGIjbpkxf6KqPY5Y2gbN98xjpcmHTrQ,2663
skimage/util/apply_parallel.py,sha256=Xr9-pBL-sOY3b-XW353UQkNMa9rxXiQqn1Q3tc3YlR4,7862
skimage/util/arraycrop.py,sha256=mtaprpcOqge3pAsaXOg9cyjiCYYPHLTArAF_ZYM72Nc,2558
skimage/util/compare.py,sha256=5zN1DSRwdLpeDlav3p3NgX_KTxDeTUFRFu0O3Xor-m0,4639
skimage/util/dtype.py,sha256=Qjm77Aiu_ZB47H6SscrhnjRLE9vHRnO7cB34wpTB0P0,18281
skimage/util/lookfor.py,sha256=uQQaNWru64U8fE4aqPIRbidHBDMqnaqkj05CGwfWA9s,820
skimage/util/noise.py,sha256=W_FzHF2eFbf32teLj3SY2XKOpHYDDm4YrvAhbGu3FRg,8796
skimage/util/shape.py,sha256=0hz-n2tsQXeQKSjgWg3T5EDdjFrmXlLjE81yvKA5YJQ,8075
skimage/util/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/util/tests/__pycache__/__init__.cpython-310.pyc,,
skimage/util/tests/__pycache__/test_apply_parallel.cpython-310.pyc,,
skimage/util/tests/__pycache__/test_arraycrop.cpython-310.pyc,,
skimage/util/tests/__pycache__/test_compare.cpython-310.pyc,,
skimage/util/tests/__pycache__/test_dtype.cpython-310.pyc,,
skimage/util/tests/__pycache__/test_invert.cpython-310.pyc,,
skimage/util/tests/__pycache__/test_labels.cpython-310.pyc,,
skimage/util/tests/__pycache__/test_lookfor.cpython-310.pyc,,
skimage/util/tests/__pycache__/test_map_array.cpython-310.pyc,,
skimage/util/tests/__pycache__/test_montage.cpython-310.pyc,,
skimage/util/tests/__pycache__/test_random_noise.cpython-310.pyc,,
skimage/util/tests/__pycache__/test_regular_grid.cpython-310.pyc,,
skimage/util/tests/__pycache__/test_shape.cpython-310.pyc,,
skimage/util/tests/__pycache__/test_slice_along_axes.cpython-310.pyc,,
skimage/util/tests/__pycache__/test_unique_rows.cpython-310.pyc,,
skimage/util/tests/test_apply_parallel.py,sha256=j25FhmwMhRWzz0yNfQGKVmMmDGhSWEG6OKbGG7m55u8,5201
skimage/util/tests/test_arraycrop.py,sha256=ubRIb3FaqqOryKCZk59TDT53LURa-G2-QRzvOe4cy30,1919
skimage/util/tests/test_compare.py,sha256=f70PVbEV9eZ-UWcKj361d_2eePZdFSo5HM9JpaZyEBo,3995
skimage/util/tests/test_dtype.py,sha256=6qJ7yeWG9HA8ae0dU8ZUIhoHa7CCANRPeEPu7rNns4U,6779
skimage/util/tests/test_invert.py,sha256=UV30Dtq46ce-WLmAf86twlLHdVuGU2unKxqUwMquQ64,2489
skimage/util/tests/test_labels.py,sha256=Gypxm_1hZY3w6SNFd8gWPhv67XwINxSBqEGVw5j3x8U,1861
skimage/util/tests/test_lookfor.py,sha256=Y4tzBxYQSaekX7zuQgAfc-0rysvU5D-0l5eu5YVkRLw,315
skimage/util/tests/test_map_array.py,sha256=59IS7c0fTku6Q4RD-_orwXvfjlSSEYKII6U400BQDzM,2987
skimage/util/tests/test_montage.py,sha256=fZxrct3tcMg-HBVifKMtU7VCKqKviyjxLGFGPbN7mEk,5853
skimage/util/tests/test_random_noise.py,sha256=kncv8h3bRNrWJZ0og6Y--rTHz6RoJJE_Z75rKeDZKxQ,7878
skimage/util/tests/test_regular_grid.py,sha256=Soyuuf63POW0HvcHCfil_OSxL0MTgKbQ0uZ_8tJmQrI,1017
skimage/util/tests/test_shape.py,sha256=eKXfb25uzan5VRL4F9Q7GOx6Cj8dkb1RJ5KMxiz7GfY,4730
skimage/util/tests/test_slice_along_axes.py,sha256=-c5N1ASZow7GbfL0hHe1QP0YPpi3TiuDiVkstSOnnI8,1744
skimage/util/tests/test_unique_rows.py,sha256=iVGVTMAK7uCzMGa75m55QAUSwvgDIBkq7OTaBiuQcvI,1137
skimage/util/unique.py,sha256=ccnl0oe1RpmyQpKUnZhlIAQyVciE8ZITsipeoV06pvo,1567

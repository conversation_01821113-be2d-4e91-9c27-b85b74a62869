import cv2
import numpy as np

def stitch_images(image_path1, image_path2, output_path="stitched_image.jpg"):
    """
    Melakukan deteksi fitur dan menggabungkan dua gambar menjadi satu.

    Args:
        image_path1 (str): Path ke gambar pertama (akan menjadi latar belakang).
        image_path2 (str): Path ke gambar kedua (akan diwarping).
        output_path (str): Path untuk menyimpan gambar yang disambung.
    """
    # 1. Muat kedua gambar
    img1 = cv2.imread(image_path1)
    img2 = cv2.imread(image_path2)

    if img1 is None or img2 is None:
        print("Error: Gambar tidak dapat dimuat. Periksa path file.")
        return

    # 2. Konversi ke grayscale untuk deteksi fitur
    img1_gray = cv2.cvtColor(img1, cv2.COLOR_BGR2GRAY)
    img2_gray = cv2.cvtColor(img2, cv2.COLOR_BGR2GRAY)

    # 3. Deteksi fitur dan deskriptor dengan ORB
    orb = cv2.ORB_create(nfeatures=5000)
    kp1, des1 = orb.detectAndCompute(img1_gray, None)
    kp2, des2 = orb.detectAndCompute(img2_gray, None)

    # 4. Cocokkan fitur menggunakan Brute-Force Matcher
    bf = cv2.BFMatcher(cv2.NORM_HAMMING, crossCheck=True)
    matches = bf.match(des1, des2)
    matches = sorted(matches, key=lambda x: x.distance)

    # 5. Ekstrak keypoints dari kecocokan terbaik
    # Kita butuh minimal 4 pasang kecocokan yang bagus untuk homografi
    if len(matches) > 10:
        src_pts = np.float32([kp1[m.queryIdx].pt for m in matches]).reshape(-1, 1, 2)
        dst_pts = np.float32([kp2[m.trainIdx].pt for m in matches]).reshape(-1, 1, 2)

        # 6. Hitung matriks homografi
        # cv2.findHomography() menemukan matriks transformasi (H)
        H, _ = cv2.findHomography(dst_pts, src_pts, cv2.RANSAC, 5.0)

        # 7. Terapkan warping perspektif pada gambar kedua
        h1, w1 = img1.shape[:2]
        h2, w2 = img2.shape[:2]

        # Ukur dimensi kanvas baru
        # Hitung sudut-sudut gambar kedua setelah di-warp
        corners2 = np.float32([[0, 0], [0, h2 - 1], [w2 - 1, h2 - 1], [w2 - 1, 0]]).reshape(-1, 1, 2)
        warped_corners = cv2.perspectiveTransform(corners2, H)

        # Temukan dimensi mozaik final
        min_x = min(0, np.min(warped_corners[:, 0, 0]))
        max_x = max(w1, np.max(warped_corners[:, 0, 0]))
        min_y = min(0, np.min(warped_corners[:, 0, 1]))
        max_y = max(h1, np.max(warped_corners[:, 0, 1]))

        new_width = int(max_x - min_x)
        new_height = int(max_y - min_y)
        
        # Buat matriks translasi untuk menggeser gambar agar tidak ada yang negatif
        translation_matrix = np.array([[1, 0, -min_x], [0, 1, -min_y], [0, 0, 1]])
        H = translation_matrix @ H

        # 8. Lakukan warping dan gabungkan gambar
        stitched_img = cv2.warpPerspective(img2, H, (new_width, new_height))
        stitched_img[-int(min_y):-int(min_y) + h1, -int(min_x):-int(min_x) + w1] = img1

        # 9. Simpan gambar hasil
        cv2.imwrite(output_path, stitched_img)
        print(f"Gambar berhasil digabungkan dan disimpan di: {output_path}")

    else:
        print("Tidak cukup kecocokan fitur yang ditemukan untuk menggabungkan gambar.")

# --- Contoh Penggunaan ---
# Ganti path berikut dengan path gambar Anda sendiri
image_path_1 = "1.png"
image_path_2 = "2.png"

stitch_images(image_path_1, image_path_2)